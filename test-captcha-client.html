<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Captcha Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Captcha Detection Client Test</h1>
        
        <div class="controls">
            <button id="connectBtn" class="btn btn-primary">Connect with Captcha Purpose</button>
            <button id="disconnectBtn" class="btn btn-secondary" disabled>Disconnect</button>
        </div>
        
        <div id="status" class="status disconnected">Disconnected</div>
        
        <h3>Connection Log:</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        class CaptchaTestClient {
            constructor() {
                this.ws = null;
                this.clientId = null;
                this.isConnected = false;
                
                this.connectBtn = document.getElementById('connectBtn');
                this.disconnectBtn = document.getElementById('disconnectBtn');
                this.status = document.getElementById('status');
                this.log = document.getElementById('log');
                
                this.setupEventListeners();
            }
            
            setupEventListeners() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
            }
            
            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.log.innerHTML += `[${timestamp}] ${message}\n`;
                this.log.scrollTop = this.log.scrollHeight;
                console.log(message);
            }
            
            updateStatus(connected) {
                this.isConnected = connected;
                this.status.textContent = connected ? 'Connected with Captcha Purpose' : 'Disconnected';
                this.status.className = `status ${connected ? 'connected' : 'disconnected'}`;
                this.connectBtn.disabled = connected;
                this.disconnectBtn.disabled = !connected;
            }
            
            connect() {
                if (this.isConnected) return;
                
                this.log('🔗 Connecting to signaling server...');
                this.ws = new WebSocket('ws://localhost:8080');
                
                this.ws.onopen = () => {
                    this.log('✅ Connected to signaling server');
                    this.updateStatus(true);
                };
                
                this.ws.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        this.handleMessage(message);
                    } catch (error) {
                        this.log(`❌ Failed to parse message: ${error.message}`);
                    }
                };
                
                this.ws.onclose = () => {
                    this.log('🔌 Disconnected from signaling server');
                    this.updateStatus(false);
                    this.clientId = null;
                };
                
                this.ws.onerror = (error) => {
                    this.log(`❌ WebSocket error: ${error.message || 'Unknown error'}`);
                };
            }
            
            disconnect() {
                if (this.ws) {
                    this.ws.close();
                    this.ws = null;
                }
            }
            
            handleMessage(message) {
                this.log(`📨 Received: ${message.type}`);
                
                switch (message.type) {
                    case 'welcome':
                        this.clientId = message.clientId;
                        this.log(`🆔 Got client ID: ${this.clientId}`);
                        
                        // Register as web client with CAPTCHA purpose
                        this.sendMessage({
                            type: 'register-web-client',
                            purpose: 'captcha',  // This is the key test!
                            metadata: {
                                userAgent: navigator.userAgent,
                                timestamp: Date.now(),
                                testClient: true
                            }
                        });
                        this.log('🔍 Registered with CAPTCHA purpose');
                        break;
                        
                    case 'available-streams':
                        this.log(`📺 Available streams: ${message.targetTabs.length}`);
                        break;
                        
                    default:
                        this.log(`ℹ️ Unknown message: ${message.type}`);
                }
            }
            
            sendMessage(message) {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify(message));
                } else {
                    this.log('⚠️ Cannot send message - not connected');
                }
            }
        }
        
        // Initialize the test client
        const client = new CaptchaTestClient();
    </script>
</body>
</html>
