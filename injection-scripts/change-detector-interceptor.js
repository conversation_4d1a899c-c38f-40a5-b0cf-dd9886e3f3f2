/**
 * Change Detector Interceptor
 *
 * A video frame interceptor that detects click events and
 * pauses the video stream when screen changes are detected, resuming only
 * after the screen has stabilized.
 *
 * Features:
 * - Click event detection
 * - Screen stability detection using pixelmatch
 * - Stream pause/resume functionality
 * - Configurable thresholds and stability duration
 * - Integration with established interceptor architecture
 */

class ChangeDetectorInterceptor extends BaseInterceptor {
  constructor(name = "change-detector", options = {}) {
    // Default configuration
    const defaultConfig = {
      debug: false,
      enabled: true,
      // Change detector settings
      changeThreshold: 5, // Percentage change to trigger pause
      stabilityThreshold: 1, // Percentage change to consider stable
      consecutiveStableFrames: 3, // Number of consecutive stable frames required
      maxWaitDuration: 5000, // Maximum time to wait for stability
      // Performance settings
      pixelSampling: 2, // Pixel sampling rate for performance
      ...options,
    };

    super(name, defaultConfig);

    // Canvas for frame processing and comparison
    this.canvas = null;
    this.ctx = null;

    // Change detector state
    this.isStreamPaused = false;
    this.isMonitoring = false;
    this.lastFrameData = null;
    this.consecutiveStableCount = 0;
    this.maxWaitTimeout = null;

    // Frame tracking
    this.pendingFrames = [];
    this.lastStableFrame = null; // Keep reference to last stable frame for pausing

    // WebSocket messaging
    this.controlTabManager = null; // Reference to control tab for WebSocket messaging
    this.triggeringWebClientId = null; // Track which web client triggered the change detection
    this.pauseStartTime = null; // Track when pause started for duration calculation

    // Simple inter-interceptor communication
    this.videoCropInterceptor = null; // Reference to video crop interceptor for direct communication

    this.log("ChangeDetectorInterceptor initialized", this.config);
  }

  /**
   * Initialize the interceptor and set up click event listeners
   */
  initialize(videoTrack) {
    const processedTrack = super.initialize(videoTrack);
    return processedTrack;
  }

  /**
   * Set the control tab manager reference for WebSocket messaging
   * @param {Object} controlTabManager - Reference to the control tab manager
   */
  setControlTabManager(controlTabManager) {
    this.controlTabManager = controlTabManager;
    this.log("Control tab manager reference set for WebSocket messaging");
  }

  /**
   * Set the reference to the video crop interceptor for direct communication
   * @param {Object} interceptor - Reference to the video crop interceptor
   */
  setVideoCropperInterceptor(interceptor) {
    this.videoCropInterceptor = interceptor;
    this.log("Video crop interceptor reference set for direct communication");
  }

  /**
   * Set the web client ID that triggered the change detection
   * @param {string} webClientId - ID of the web client that triggered change detection
   */
  setTriggeringWebClient(webClientId) {
    this.triggeringWebClientId = webClientId;
    this.log(`Triggering web client set: ${webClientId}`);
  }

  /**
   * Send WebSocket notification to the triggering web client
   * @param {string} type - Message type
   * @param {Object} data - Additional message data
   */
  sendWebSocketNotification(type, data = {}) {
    if (!this.controlTabManager || !this.triggeringWebClientId) {
      this.log(
        "Cannot send WebSocket notification - missing control tab manager or web client ID"
      );
      return;
    }

    const message = {
      type: type,
      webClientId: this.triggeringWebClientId,
      timestamp: Date.now(),
      ...data,
    };

    this.log(
      `Sending WebSocket notification: ${type} to client ${this.triggeringWebClientId}`
    );
    this.controlTabManager.sendMessage(message);
  }

  /**
   * Process a video frame - implements BaseInterceptor interface
   * @param {VideoFrame} frame - Input video frame
   * @returns {VideoFrame} - Processed video frame (may be paused)
   */
  async processVideoFrame(frame) {
    try {
      // If not monitoring, pass through
      if (!this.isMonitoring) {
        return frame;
      }

      // Initialize canvas if needed
      if (
        !this.canvas ||
        this.canvas.width !== frame.codedWidth ||
        this.canvas.height !== frame.codedHeight
      ) {
        this.initializeCanvas(frame.codedWidth, frame.codedHeight);
      }

      // Draw frame to canvas and get image data
      this.ctx.drawImage(frame, 0, 0);
      const imageData = this.ctx.getImageData(
        0,
        0,
        this.canvas.width,
        this.canvas.height
      );
      const currentFrameData = imageData.data;

      // Compare with previous frame if available
      if (this.lastFrameData) {
        const changePercentage = this.compareFrames(
          currentFrameData,
          this.lastFrameData
        );

        // Store current frame data for next comparison
        this.lastFrameData = new Uint8ClampedArray(currentFrameData);
        if (this.config.debug) {
          this.log(`Frame comparison: ${changePercentage.toFixed(2)}% change`);
        }

        // Check if significant change detected - immediately pause and return held frame
        if (
          changePercentage > this.config.changeThreshold &&
          !this.isStreamPaused
        ) {
          this.log(
            `📊 Significant change detected: ${changePercentage.toFixed(
              2
            )}% > ${this.config.changeThreshold}% - immediately pausing`
          );
          this.pauseStream();

          // Immediately return held frame - don't send the changed frame
          const heldFrame = this.createHeldFrame(this.lastFrameData);
          if (heldFrame !== this.lastFrameData) {
            frame.close(); // Close the changed frame
          }
          return heldFrame;
        }
        // Check if screen has stabilized
        else if (
          this.isStreamPaused &&
          changePercentage <= this.config.stabilityThreshold
        ) {
          this.handleStableFrame();
        }
        // Reset stability if change detected while monitoring stability
        else if (
          this.isStreamPaused &&
          changePercentage > this.config.stabilityThreshold
        ) {
          this.resetStability();
        }
        // Log current state for debugging
        else if (this.config.debug && this.isStreamPaused) {
          this.log(
            `🔄 Still paused, change: ${changePercentage.toFixed(
              2
            )}%, waiting for stability`
          );
        }
      }

      // Store current frame data for next comparison
      this.lastFrameData = new Uint8ClampedArray(currentFrameData);

      // If not paused, store this as the last stable frame
      if (!this.isStreamPaused) {
        this.updateLastStableFrame(frame);
        return frame; // Send the frame normally
      }

      // If we're paused (from previous detection), return held frame
      const heldFrame = this.createHeldFrame(frame);
      if (heldFrame !== frame) {
        frame.close(); // Close the current frame
      }
      return heldFrame;
    } catch (error) {
      this.log("Error processing frame:", error);
      return frame;
    }
  }

  /**
   * Initialize canvas for frame processing
   * @param {number} width - Frame width
   * @param {number} height - Frame height
   */
  initializeCanvas(width, height) {
    this.canvas = document.createElement("canvas");
    this.canvas.width = width;
    this.canvas.height = height;
    this.ctx = this.canvas.getContext("2d");

    this.log(`Canvas initialized: ${width}x${height}`);
  }

  /**
   * Update the last stable frame reference
   * @param {VideoFrame} frame - The frame to store as stable
   */
  updateLastStableFrame(frame) {
    // Close previous stable frame if it exists
    if (this.lastStableFrame) {
      try {
        this.lastStableFrame.close();
      } catch (e) {
        // Frame may already be closed
      }
    }

    // Clone the frame for holding
    try {
      this.lastStableFrame = new VideoFrame(frame, {
        timestamp: frame.timestamp,
        duration: frame.duration,
      });
    } catch (error) {
      this.log("Error cloning stable frame:", error);
      this.lastStableFrame = null;
    }
  }

  /**
   * Create a held frame to return when stream is paused
   * @param {VideoFrame} fallbackFrame - Fallback frame if no stable frame available
   * @returns {VideoFrame} - Held frame or fallback frame
   */
  createHeldFrame(fallbackFrame = null) {
    if (!this.lastStableFrame) {
      // If no stable frame available, return the fallback frame
      return fallbackFrame;
    }

    try {
      // Create a new frame with current timestamp to maintain timing
      return new VideoFrame(this.lastStableFrame, {
        timestamp: performance.now() * 1000, // Convert to microseconds
        duration: this.lastStableFrame.duration,
      });
    } catch (error) {
      this.log("Error creating held frame:", error);
      // Return fallback frame if held frame creation fails
      return fallbackFrame;
    }
  }

  /**
   * Compare two frame data arrays using pixelmatch or simple comparison
   * @param {Uint8ClampedArray} currentData - Current frame data
   * @param {Uint8ClampedArray} previousData - Previous frame data
   * @returns {number} - Percentage of pixels that differ
   */
  compareFrames(currentData, previousData) {
    if (currentData.length !== previousData.length) {
      return 100; // 100% different if sizes don't match
    }

    // Use pixelmatch if available
    if (typeof window.pixelmatch === "function") {
      const diffCount = window.pixelmatch(
        previousData,
        currentData,
        null, // No diff output needed
        this.canvas.width,
        this.canvas.height,
        {
          threshold: 0.1,
          includeAA: false,
          alpha: 0.1,
          aaColor: [255, 255, 0],
          diffColor: [255, 0, 0],
          diffColorAlt: null,
        }
      );

      const totalPixels = this.canvas.width * this.canvas.height;
      return (diffCount / totalPixels) * 100;
    }

    // Fallback to simple pixel comparison
    let diffCount = 0;
    const totalPixels = currentData.length / 4;
    const sampling = this.config.pixelSampling || 1;

    for (let i = 0; i < currentData.length; i += 4 * sampling) {
      const r1 = currentData[i];
      const g1 = currentData[i + 1];
      const b1 = currentData[i + 2];
      const r2 = previousData[i];
      const g2 = previousData[i + 1];
      const b2 = previousData[i + 2];

      const diff = Math.abs(r1 - r2) + Math.abs(g1 - g2) + Math.abs(b1 - b2);
      if (diff > 30) {
        // Threshold for considering pixels different
        diffCount++;
      }
    }

    return (diffCount / (totalPixels / sampling)) * 100;
  }

  /**
   * Pause the video stream when significant change is detected
   */
  pauseStream() {
    if (this.isStreamPaused) return;

    this.isStreamPaused = true;
    this.consecutiveStableCount = 0; // Reset stability tracking
    this.pauseStartTime = Date.now(); // Track when pause started

    this.log(
      "🔴 Stream paused due to significant screen change - waiting for stability"
    );

    // Send WebSocket notification to web client
    this.sendWebSocketNotification("stream-paused", {
      reason: "screen-change-detected",
      changeThreshold: this.config.changeThreshold,
      stabilityThreshold: this.config.stabilityThreshold,
      consecutiveStableFramesRequired: this.config.consecutiveStableFrames,
    });
  }

  /**
   * Handle a stable frame during monitoring
   */
  handleStableFrame() {
    this.consecutiveStableCount++;

    if (this.consecutiveStableCount === 1) {
      this.log("🟡 Screen stability detection started");
    }

    this.log(
      `📈 Stable frame ${this.consecutiveStableCount}/${this.config.consecutiveStableFrames}`
    );

    // Check if we have enough consecutive stable frames
    if (this.consecutiveStableCount >= this.config.consecutiveStableFrames) {
      this.log("🟢 Screen stability criteria met - resuming stream");

      // Update crop region with random values when stability is achieved
      this.updateCropRegionToRandom();

      this.resumeStream();
    }
  }

  /**
   * Update the video crop interceptor with random crop values
   */
  updateCropRegionToRandom() {
    if (this.videoCropInterceptor) {
      // Generate random crop region
      const randomCrop = {
        x: Math.floor(Math.random() * 500), // Random x position
        y: Math.floor(Math.random() * 300), // Random y position
        width: 400 + Math.floor(Math.random() * 400), // Random width between 400-800
        height: 300 + Math.floor(Math.random() * 300), // Random height between 300-600
      };

      this.log("🎲 Updating crop region to random values:", randomCrop);
      this.videoCropInterceptor.setCropRegion(randomCrop);
    } else {
      this.log(
        "⚠️ Video crop interceptor not available for random crop update"
      );
    }
  }

  /**
   * Reset stability tracking when change is detected during stability monitoring
   */
  resetStability() {
    if (this.consecutiveStableCount > 0) {
      this.log("🔄 Screen stability reset - change detected during monitoring");
      this.consecutiveStableCount = 0;
    }
  }

  /**
   * Resume the video stream and disable the interceptor
   */
  resumeStream() {
    const wasMonitoring = this.isMonitoring;

    // Reset all monitoring state
    this.isStreamPaused = false;
    this.isMonitoring = false;
    this.consecutiveStableCount = 0;
    this.lastFrameData = null; // Clear for next session

    // Clear timeout
    if (this.maxWaitTimeout) {
      clearTimeout(this.maxWaitTimeout);
      this.maxWaitTimeout = null;
    }

    // Release any pending frames
    this.pendingFrames.forEach((frame) => {
      try {
        frame.close();
      } catch (e) {
        // Frame may already be closed
      }
    });
    this.pendingFrames = [];

    // Release the last stable frame
    if (this.lastStableFrame) {
      try {
        this.lastStableFrame.close();
      } catch (e) {
        // Frame may already be closed
      }
      this.lastStableFrame = null;
    }

    // Calculate pause duration
    const pauseDuration = this.pauseStartTime
      ? Date.now() - this.pauseStartTime
      : 0;
    this.pauseStartTime = null; // Reset pause start time

    // Disable the interceptor so it doesn't monitor until next click
    this.config.enabled = false;

    if (wasMonitoring) {
      this.log("✅ Stream resumed - screen stabilized, interceptor disabled");

      // Send WebSocket notification to web client
      this.sendWebSocketNotification("stream-resumed", {
        reason: "screen-stabilized",
        pauseDuration: pauseDuration,
        consecutiveStableFramesAchieved: this.config.consecutiveStableFrames,
      });
    }
  }

  /**
   * Handle maximum wait timeout - auto-resume and disable interceptor
   */
  onMaxWaitReached() {
    this.log(
      "⏰ Maximum wait duration reached, auto-resuming stream and disabling interceptor"
    );

    // Calculate pause duration
    const pauseDuration = this.pauseStartTime
      ? Date.now() - this.pauseStartTime
      : 0;

    // Send timeout notification before resuming
    this.sendWebSocketNotification("stream-resumed", {
      reason: "timeout-reached",
      pauseDuration: pauseDuration,
      maxWaitDuration: this.config.maxWaitDuration,
    });

    this.resumeStream();
  }

  /**
   * Create a paused frame (duplicate of the last frame)
   * @param {VideoFrame} originalFrame - Original frame to duplicate
   * @returns {VideoFrame} - Paused frame
   */
  createPausedFrame(originalFrame) {
    try {
      // Create a new frame with the same timestamp to maintain timing
      return new VideoFrame(this.canvas, {
        timestamp: originalFrame.timestamp,
        duration: originalFrame.duration,
      });
    } catch (error) {
      this.log("Error creating paused frame:", error);
      return originalFrame;
    }
  }

  /**
   * Override cleanup to handle canvas and timeout resources
   */
  cleanup() {
    this.log("Cleaning up change detector interceptor...");

    // Clear timeouts
    if (this.maxWaitTimeout) {
      clearTimeout(this.maxWaitTimeout);
      this.maxWaitTimeout = null;
    }

    // Clean up canvas resources
    if (this.canvas) {
      this.canvas.width = 0;
      this.canvas.height = 0;
      this.canvas = null;
    }

    this.ctx = null;
    this.lastFrameData = null;

    // Release pending frames
    this.pendingFrames.forEach((frame) => {
      try {
        frame.close();
      } catch (e) {
        // Frame may already be closed
      }
    });
    this.pendingFrames = [];

    // Release the last stable frame
    if (this.lastStableFrame) {
      try {
        this.lastStableFrame.close();
      } catch (e) {
        // Frame may already be closed
      }
      this.lastStableFrame = null;
    }

    // Reset state
    this.isStreamPaused = false;
    this.isMonitoring = false;
    this.consecutiveStableCount = 0;

    // Call parent cleanup
    super.cleanup();

    this.log("Change detector interceptor cleanup complete");
  }

  /**
   * Get interceptor-specific status
   * @returns {Object} - Status information
   */
  getStatus() {
    return {
      ...this.getMetadata(),
      isStreamPaused: this.isStreamPaused,
      isMonitoring: this.isMonitoring,
      consecutiveStableCount: this.consecutiveStableCount,
      canvasInitialized: !!this.canvas,
      canvasSize: this.canvas
        ? `${this.canvas.width}x${this.canvas.height}`
        : null,
      pendingFrames: this.pendingFrames.length,
    };
  }

  /**
   * Start monitoring for screen changes (called when interceptor is enabled)
   */
  startMonitoring() {
    if (this.isMonitoring) {
      this.log("Monitoring already active");
      return;
    }

    this.log("🖱️ Click detected - starting screen change monitoring");

    // Reset all state for new monitoring session
    this.isMonitoring = true;
    this.isStreamPaused = false;
    this.consecutiveStableCount = 0;
    this.lastFrameData = null; // Reset to ensure fresh comparison

    // Clear any existing timeout
    if (this.maxWaitTimeout) {
      clearTimeout(this.maxWaitTimeout);
    }

    // Set maximum wait timeout to auto-disable if stability not achieved
    this.maxWaitTimeout = setTimeout(() => {
      this.onMaxWaitReached();
    }, this.config.maxWaitDuration);

    this.log(
      `Change detector monitoring enabled for ${this.config.maxWaitDuration}ms`
    );
  }

  /**
   * Stop monitoring and reset state
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    this.log("Stopping screen change monitoring");
    this.resumeStream(); // This will reset all state
  }

  /**
   * Update configuration
   * @param {Object} newConfig - New configuration options
   */
  updateConfig(newConfig) {
    const wasEnabled = this.config.enabled;
    super.updateConfig(newConfig);

    // Validate thresholds
    if (this.config.changeThreshold < 0) {
      this.config.changeThreshold = 0;
    }
    if (this.config.stabilityThreshold < 0) {
      this.config.stabilityThreshold = 0;
    }

    // If interceptor was just enabled, start monitoring
    if (!wasEnabled && this.config.enabled) {
      this.startMonitoring();
    }
    // If interceptor was disabled, stop monitoring
    else if (wasEnabled && !this.config.enabled) {
      this.stopMonitoring();
    }
  }
}

// Export for use in other modules
if (typeof module !== "undefined" && module.exports) {
  module.exports = ChangeDetectorInterceptor;
} else if (typeof window !== "undefined") {
  window.ChangeDetectorInterceptor = ChangeDetectorInterceptor;
}
