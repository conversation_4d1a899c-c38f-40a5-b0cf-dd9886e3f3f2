/**
 * CDP Manager - Abstracted Chrome DevTools Protocol Management
 * 
 * Provides a clean API for managing CDP connections and event handling
 * Abstracts away the complexity of CDP session management from the control tab
 */

class CDP {
  constructor(targetInfo) {
    this.targetInfo = targetInfo;
    this.ws = null;
    this.messageId = 0;
    this.pendingMessages = new Map();
  }

  async connect() {
    if (this.ws) return;

    this.ws = new WebSocket(this.targetInfo.webSocketDebuggerUrl);

    return new Promise((resolve, reject) => {
      this.ws.onopen = () => {
        console.log(`Connected to target: ${this.targetInfo.title}`);
        resolve();
      };

      this.ws.onerror = reject;

      this.ws.onmessage = (event) => {
        const message = JSON.parse(event.data);

        if (message.id && this.pendingMessages.has(message.id)) {
          const { resolve, reject } = this.pendingMessages.get(message.id);
          this.pendingMessages.delete(message.id);

          if (message.error) {
            reject(new Error(message.error.message));
          } else {
            resolve(message.result);
          }
        }
      };
    });
  }

  async send(method, params = {}, sessionId = null) {
    if (!this.ws) {
      await this.connect();
    }

    const id = ++this.messageId;
    const message = { id, method, params };

    if (sessionId) {
      message.sessionId = sessionId;
    }

    return new Promise((resolve, reject) => {
      this.pendingMessages.set(id, { resolve, reject });
      this.ws.send(JSON.stringify(message));
    });
  }

  get Runtime() {
    return {
      enable: (params = {}, sessionId = null) =>
        this.send("Runtime.enable", params, sessionId),
      evaluate: (params, sessionId = null) =>
        this.send("Runtime.evaluate", params, sessionId),
    };
  }

  get Target() {
    return {
      getTargets: (params = {}, sessionId = null) =>
        this.send("Target.getTargets", params, sessionId),
      createTarget: (params, sessionId = null) =>
        this.send("Target.createTarget", params, sessionId),
      attachToTarget: (params, sessionId = null) =>
        this.send("Target.attachToTarget", params, sessionId),
      closeTarget: (params, sessionId = null) =>
        this.send("Target.closeTarget", params, sessionId),
      activateTarget: (params, sessionId = null) =>
        this.send("Target.activateTarget", params, sessionId),
    };
  }

  get Input() {
    return {
      dispatchKeyEvent: (params, sessionId = null) =>
        this.send("Input.dispatchKeyEvent", params, sessionId),
      dispatchMouseEvent: (params, sessionId = null) =>
        this.send("Input.dispatchMouseEvent", params, sessionId),
      synthesizeScrollGesture: (params, sessionId = null) =>
        this.send("Input.synthesizeScrollGesture", params, sessionId),
    };
  }

  async close() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

/**
 * CDP Manager - High-level API for CDP operations
 */
export class CDPManager {
  constructor(options = {}) {
    this.options = {
      debugPort: options.debugPort || 9222,
      debug: options.debug || false,
      ...options,
    };

    this.connections = new Map(); // targetTabId -> { client, sessionId, targetInfo }
    this.eventHandlers = new Map(); // eventType -> handler function
    
    this.log("[CDP-Manager] Initialized");
  }

  /**
   * Log messages with CDP-Manager prefix
   */
  log(message, ...args) {
    if (this.options.debug) {
      console.log(`[CDP-Manager] ${message}`, ...args);
    }
  }

  /**
   * Add a new CDP connection to a target tab
   * @param {string} targetTabId - The target tab ID
   * @param {Object} targetInfo - Target information (optional, will be fetched if not provided)
   * @returns {Promise<Object>} Connection info with client and sessionId
   */
  async addConnection(targetTabId, targetInfo = null) {
    try {
      if (this.connections.has(targetTabId)) {
        return this.connections.get(targetTabId);
      }

      if (!targetInfo) {
        targetInfo = await this.getTargetInfo(targetTabId);
      }

      if (!targetInfo) {
        targetInfo = {
          id: targetTabId,
          title: `Target Tab ${targetTabId.substring(0, 8)}`,
          url: "about:blank",
          webSocketDebuggerUrl: `ws://localhost:${this.options.debugPort}/devtools/page/${targetTabId}`,
          type: "page"
        };
      }

      const client = new CDP(targetInfo);
      await client.connect();

      const attachResult = await client.Target.attachToTarget({
        targetId: targetTabId,
        flatten: true,
      });

      const sessionId = attachResult.sessionId;

      const connection = {
        client,
        sessionId,
        targetInfo,
        createdAt: Date.now(),
      };

      this.connections.set(targetTabId, connection);
      this.log(`CDP connection established for tab: ${targetTabId}`);
      return connection;
    } catch (error) {
      this.log(`Failed to add connection to tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Remove a CDP connection
   * @param {string} targetTabId - The target tab ID
   */
  async removeConnection(targetTabId) {
    try {
      const connection = this.connections.get(targetTabId);
      if (!connection) {
        return;
      }

      await connection.client.close();

      this.connections.delete(targetTabId);
      this.log(`CDP connection removed for tab: ${targetTabId}`);
    } catch (error) {
      this.log(`Failed to remove connection from tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Get connection info for a target tab
   * @param {string} targetTabId - The target tab ID
   * @returns {Object|null} Connection info or null if not found
   */
  getConnection(targetTabId) {
    return this.connections.get(targetTabId) || null;
  }

  /**
   * Get all active connections
   * @returns {Map} Map of targetTabId -> connection info
   */
  getAllConnections() {
    return new Map(this.connections);
  }

  /**
   * Execute a command on a target tab
   * @param {string} targetTabId - The target tab ID
   * @param {string} method - The CDP method to execute
   * @param {Object} params - Parameters for the method
   * @returns {Promise<any>} Result of the command
   */
  async executeCommand(targetTabId, method, params = {}) {
    try {
      const connection = this.connections.get(targetTabId);
      if (!connection) {
        throw new Error(`No connection found for tab: ${targetTabId}`);
      }

      // Parse method to determine domain and method
      const [domain, methodName] = method.split('.');
      
      if (!connection.client[domain]) {
        throw new Error(`Unsupported domain: ${domain}`);
      }

      const result = await connection.client[domain][methodName](params, connection.sessionId);
      return result;
    } catch (error) {
      this.log(`Failed to execute ${method} on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Get target tab information
   * @param {string} targetTabId - The target tab ID
   * @returns {Promise<Object>} Tab information including dimensions
   */
  async getTargetTabInfo(targetTabId) {
    try {
      const result = await this.executeCommand(targetTabId, "Runtime.evaluate", {
        expression: `({
          width: window.innerWidth,
          height: window.innerHeight,
          url: window.location.href,
          title: document.title
        })`,
        returnByValue: true,
        awaitPromise: true,
      });

      return result.result.value;
    } catch (error) {
      this.log(`Failed to get tab info for ${targetTabId}:`, error);
      // Return default values as fallback
      return { width: 1920, height: 1080, url: "unknown", title: "Unknown" };
    }
  }

  /**
   * Execute JavaScript on a target tab
   * @param {string} targetTabId - The target tab ID
   * @param {string} script - JavaScript code to execute
   * @returns {Promise<any>} Result of the script execution
   */
  async executeScript(targetTabId, script) {
    try {
      const result = await this.executeCommand(targetTabId, "Runtime.evaluate", {
        expression: script,
        returnByValue: true,
      });

      return result.result.value;
    } catch (error) {
      this.log(`Failed to execute script on tab ${targetTabId}:`, error);
      return null;
    }
  }

  /**
   * Register an event handler for user events
   * @param {string} eventType - The event type (e.g., 'click', 'scroll', 'keydown')
   * @param {Function} handler - The handler function
   */
  registerEventHandler(eventType, handler) {
    this.eventHandlers.set(eventType, handler);
  }

  /**
   * Unregister an event handler
   * @param {string} eventType - The event type
   */
  unregisterEventHandler(eventType) {
    this.eventHandlers.delete(eventType);
  }

  /**
   * Handle a user event by dispatching it to the appropriate target tab
   * @param {Object} userEvent - The user event object
   * @param {string} targetTabId - The target tab ID
   */
  async handleUserEvent(userEvent, targetTabId) {
    try {
      // Check if we have a connection to this target tab
      const connection = this.connections.get(targetTabId);
      if (!connection) {
        throw new Error(`No CDP connection found for tab: ${targetTabId}`);
      }

      // Get the appropriate handler for this event type
      const handler = this.eventHandlers.get(userEvent.eventType);
      if (!handler) {
        this.log(`No handler registered for event type: ${userEvent.eventType}`);
        return;
      }

      // Execute the handler
      await handler(userEvent, targetTabId);
    } catch (error) {
      this.log(`Failed to handle user event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Built-in event handlers
   */

  /**
   * Handle click events
   * @param {Object} userEvent - The click event
   * @param {string} targetTabId - The target tab ID
   */
  async handleClickEvent(userEvent, targetTabId) {
    try {
      // Get target tab dimensions
      const tabInfo = await this.getTargetTabInfo(targetTabId);
      if (!tabInfo) {
        throw new Error("Could not get target tab info");
      }

      // Calculate actual coordinates in target tab
      const targetX = userEvent.x * tabInfo.width;
      const targetY = userEvent.y * tabInfo.height;

      // Dispatch mouse down
      const mouseDownParams = {
        type: "mousePressed",
        x: Math.round(targetX),
        y: Math.round(targetY),
        button: "left",
        clickCount: 1,
        buttons: 1,
      };
      
      await this.executeCommand(targetTabId, "Input.dispatchMouseEvent", mouseDownParams);

      // Small delay between mouse down and up
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Dispatch mouse up
      const mouseUpParams = {
        type: "mouseReleased",
        x: Math.round(targetX),
        y: Math.round(targetY),
        button: "left",
        clickCount: 1,
        buttons: 0,
      };
      
      await this.executeCommand(targetTabId, "Input.dispatchMouseEvent", mouseUpParams);
    } catch (error) {
      this.log(`Failed to handle click event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Handle scroll events
   * @param {Object} userEvent - The scroll event
   * @param {string} targetTabId - The target tab ID
   */
  async handleScrollEvent(userEvent, targetTabId) {
    try {
      // Get target tab dimensions
      const tabInfo = await this.getTargetTabInfo(targetTabId);
      if (!tabInfo) {
        throw new Error("Could not get target tab info");
      }

      // Calculate actual coordinates in target tab
      const targetX = userEvent.x * tabInfo.width;
      const targetY = userEvent.y * tabInfo.height;

      // Dispatch mouse wheel event
      await this.executeCommand(targetTabId, "Input.dispatchMouseEvent", {
        type: "mouseWheel",
        x: Math.round(targetX),
        y: Math.round(targetY),
        deltaX: userEvent.deltaX || 0,
        deltaY: userEvent.deltaY || 0,
      });
    } catch (error) {
      this.log(`Failed to handle scroll event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Handle key events
   * @param {Object} userEvent - The key event
   * @param {string} targetTabId - The target tab ID
   */
  async handleKeyEvent(userEvent, targetTabId) {
    try {
      const cdpEventType = this.mapBrowserEventTypeToCDP(userEvent.eventType);

      const cdpParams = {
        type: cdpEventType,
        key: userEvent.key,
        code: userEvent.code,
        keyCode: userEvent.keyCode,
      };

      if (cdpEventType === "keyDown" && userEvent.text) {
        cdpParams.text = userEvent.text;
      }

      await this.executeCommand(targetTabId, "Input.dispatchKeyEvent", cdpParams);
    } catch (error) {
      this.log(`Failed to handle key event on tab ${targetTabId}:`, error);
      throw error;
    }
  }

  /**
   * Handle scroll gesture events using synthesizeScrollGesture (for mobile touch)
   * @param {Object} userEvent - The scroll gesture event
   * @param {string} targetTabId - The target tab ID
   */
  async handleScrollGestureEvent(userEvent, targetTabId) {
    try {
      const tabInfo = await this.getTargetTabInfo(targetTabId);
      if (!tabInfo) {
        throw new Error("Could not get target tab info");
      }

      const targetX = userEvent.x * tabInfo.width;
      const targetY = userEvent.y * tabInfo.height;

      // The CDP protocol expects a negative yDistance to scroll DOWN
      // (content moves up), which is the opposite of the touch event's deltaY
      const yDistance = -userEvent.deltaY;

      await this.executeCommand(targetTabId, "Input.synthesizeScrollGesture", {
        x: Math.round(targetX),
        y: Math.round(targetY),
        yDistance: yDistance,
        xDistance: 0,
        speed: 800,
        gestureSourceType: "touch",
      });

      this.log(`Scroll gesture executed on tab ${targetTabId}: deltaY=${userEvent.deltaY}, yDistance=${yDistance}`);
    } catch (error) {
      this.log(`Failed to handle scroll gesture on tab ${targetTabId}:`, error);
    }
  }


  /**
   * Map browser event types to CDP event types
   * @param {string} browserEventType - Browser event type (keydown, keyup, keypress)
   * @returns {string} CDP event type (keyDown, keyUp, etc.)
   */
  mapBrowserEventTypeToCDP(browserEventType) {
    const eventTypeMap = {
      'keydown': 'keyDown',
      'keyup': 'keyUp',
      'keypress': 'keyDown'
    };
    
    return eventTypeMap[browserEventType] || 'keyDown';
  }

  /**
   * Get target info from CDP debug port
   * @param {string} targetTabId - The target tab ID
   * @returns {Promise<Object|null>} Target information
   */
  async getTargetInfo(targetTabId) {
    try {
      const response = await fetch(`http://localhost:${this.options.debugPort}/json`);
      const targets = await response.json();
      return targets.find((tab) => tab.id === targetTabId) || null;
    } catch (error) {
      this.log(`Failed to get target info for ${targetTabId}:`, error);

      return {
        id: targetTabId,
        title: `Target Tab ${targetTabId.substring(0, 8)}`,
        url: "about:blank",
        webSocketDebuggerUrl: `ws://localhost:${this.options.debugPort}/devtools/page/${targetTabId}`,
        type: "page"
      };
    }
  }

  /**
   * Initialize default event handlers
   */
  initializeDefaultHandlers() {
    this.registerEventHandler('click', this.handleClickEvent.bind(this));
    this.registerEventHandler('scroll', this.handleScrollEvent.bind(this));
    this.registerEventHandler('scrollGesture', this.handleScrollGestureEvent.bind(this));
    this.registerEventHandler('keydown', this.handleKeyEvent.bind(this));
    this.registerEventHandler('keyup', this.handleKeyEvent.bind(this));
    this.registerEventHandler('keypress', this.handleKeyEvent.bind(this));
  }

  /**
   * Clean up all connections
   */
  async cleanup() {
    const cleanupPromises = Array.from(this.connections.keys()).map(targetTabId =>
      this.removeConnection(targetTabId)
    );

    await Promise.all(cleanupPromises);
    
    this.eventHandlers.clear();
    this.log("CDP Manager cleanup completed");
  }

  /**
   * Get connection statistics
   * @returns {Object} Connection statistics
   */
  getStats() {
    return {
      totalConnections: this.connections.size,
      eventHandlers: this.eventHandlers.size,
      connections: Array.from(this.connections.entries()).map(([tabId, conn]) => ({
        tabId,
        sessionId: conn.sessionId,
        createdAt: conn.createdAt,
        targetInfo: conn.targetInfo,
      })),
    };
  }
}

export { CDP };
export default CDPManager;
