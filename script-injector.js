/**
 * <PERSON>ript Injection System for POC Streaming
 *
 * Handles injection of JavaScript into target tabs via CDP
 * Uses both Runtime.evaluate and Page.addScriptToEvaluateOnNewDocument
 * for persistence across reloads and redirects
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Load and cache the bundle manifest for dynamic file resolution
 */
let bundleManifest = null;

function loadBundleManifest() {
  if (!bundleManifest) {
    try {
      const manifestPath = path.join(process.cwd(), "bundle/manifest.json");
      const manifestContent = fs.readFileSync(manifestPath, "utf8");
      bundleManifest = JSON.parse(manifestContent);
      console.log("📦 Bundle manifest loaded:", bundleManifest);
    } catch (error) {
      console.error("❌ Failed to load bundle manifest:", error);
      // Fallback to empty manifest
      bundleManifest = {};
    }
  }
  return bundleManifest;
}

/**
 * Resolve bundle filename using manifest
 */
function resolveBundleFile(originalName) {
  const manifest = loadBundleManifest();
  const resolvedName = manifest[originalName] || originalName;
  console.log(`📦 Resolved ${originalName} -> ${resolvedName}`);
  return resolvedName;
}

export function wrapForMainFrameOnly(script) {
  return `
    (function() {
      if (window.parent !== window) {
        return;
      }
      ${script}
    })();
  `;
}

export class ScriptInjector {
  constructor(browserManager, signalingServer) {
    this.browserManager = browserManager;
    this.signalingServer = signalingServer;
    this.injectedScripts = new Map(); // tabId -> { scriptId, isInjected }
    this.persistentScripts = new Map(); // tabId -> scriptId for new document scripts
    this.controlTabInjected = false;
  }

  /**
   * Get the external streamer script for target tabs
   */
  async getTargetTabStreamerScript(signalingServerUrl, tabId) {
    const bundleFileName = resolveBundleFile("tab-streamer-bundle.min.js");
    const scriptPath = path.join(process.cwd(), "bundle", bundleFileName);
    let scriptContent = await fs.promises.readFile(scriptPath, "utf8");

    // Replace placeholders
    scriptContent = scriptContent.replace(
      "${KAKU_WS_ENDPOINT}",
      signalingServerUrl
    );
    scriptContent = scriptContent.replace("${TAB_ID}", tabId);
    scriptContent = scriptContent.replace("${AUTO_INITIALIZE}", false);

    return scriptContent;
  }

  async getTargetTabStreamerScriptInitializer(signalingServerUrl, tabId) {
    const scriptPath = path.join(
      process.cwd(),
      "injection-scripts/target-tab-streamer.js"
    );
    let scriptContent = await fs.promises.readFile(scriptPath, "utf8");

    // Replace placeholders
    scriptContent = scriptContent.replace(
      "${SIGNALING_SERVER_URL}",
      signalingServerUrl
    );
    scriptContent = scriptContent.replace("${TAB_ID}", tabId);
    scriptContent = scriptContent.replace("${AUTO_INITIALIZE}", true);

    return scriptContent;
  }

  /**
   * Get the control tab script with optional bundles based on client purposes
   */
  getControlTabScript(
    signalingServerUrl,
    browserWsUrl,
    includeCaptchaDetector = false
  ) {
    // Base bundle is always required
    const baseBundleFile = resolveBundleFile("base-bundle.min.js");
    const scriptFiles = [`bundle/${baseBundleFile}`];

    // Conditionally add captcha detector bundle
    if (includeCaptchaDetector) {
      const captchaBundleFile = resolveBundleFile(
        "captcha-detector-bundle.min.js"
      );
      scriptFiles.push(`bundle/${captchaBundleFile}`);
    }

    let combinedScript = "";

    // Load each script file
    for (const scriptFile of scriptFiles) {
      const scriptPath = path.join(__dirname, scriptFile);

      // Check if file exists before trying to read it
      if (fs.existsSync(scriptPath)) {
        let content = fs.readFileSync(scriptPath, "utf8");

        // Replace signaling server URL in control tab script
        if (scriptFile.includes("base-bundle")) {
          content = content.replace("${KAKU_WS_ENDPOINT}", signalingServerUrl);
          content = content.replace("${BROWSER_WS_ENDPOINT}", browserWsUrl);
        }

        combinedScript += `\n// === ${scriptFile} ===\n${content}\n`;
      } else {
        console.warn(`⚠️  Script file not found: ${scriptFile}`);
      }
    }

    return combinedScript;
  }

  /**
   * Get a specific bundle script for dynamic injection
   */
  getBundleScript(bundleName) {
    const bundleFileName = resolveBundleFile(bundleName);
    const scriptPath = path.join(__dirname, "bundle", bundleFileName);

    if (fs.existsSync(scriptPath)) {
      return fs.readFileSync(scriptPath, "utf8");
    } else {
      console.warn(
        `⚠️  Bundle file not found: ${bundleName} -> ${bundleFileName}`
      );
      return null;
    }
  }

  /**
   * Inject script into control tab with optional captcha detector based on connected clients
   */
  async injectControlTabScript(signalingServerUrl) {
    const controlTab = this.browserManager.controlTab;
    if (!controlTab) {
      throw new Error("Control tab not found");
    }

    if (this.controlTabInjected) {
      console.log("Control tab script already injected");
      return;
    }

    console.log("💉 Injecting control tab script...");

    const script = this.getControlTabScript(
      signalingServerUrl,
      this.browserManager.browserWsUrl
    );

    try {
      // Inject into current page
      const result = await controlTab.cdp.Runtime.evaluate({
        expression: script,
        awaitPromise: false,
        returnByValue: false,
      });

      if (result.exceptionDetails) {
        console.error(
          "Control tab script injection error:",
          result.exceptionDetails
        );
        throw new Error(
          `Control tab script injection failed: ${result.exceptionDetails.text}`
        );
      }

      // Try to add script for new document loads (optional - may not be supported in all Chrome versions)
      try {
        await controlTab.cdp.Page.addScriptToEvaluateOnNewDocument({
          source: wrapForMainFrameOnly(script),
        });
        console.log("✅ Persistent script added for control tab");
      } catch (persistentError) {
        console.log(
          "⚠️  Persistent script not supported, using immediate injection only"
        );
      }

      this.controlTabInjected = true;
      console.log("✅ Control tab script injected successfully");
    } catch (error) {
      console.error("❌ Failed to inject control tab script:", error);
      throw error;
    }
  }

  /**
   * Dynamically inject a bundle into the control tab
   */
  async injectBundleIntoControlTab(bundleName) {
    const controlTab = this.browserManager.controlTab;
    if (!controlTab) {
      throw new Error("Control tab not found");
    }

    console.log(`💉 Dynamically injecting bundle: ${bundleName}`);

    const bundleScript = this.getBundleScript(bundleName);
    if (!bundleScript) {
      throw new Error(`Bundle not found: ${bundleName}`);
    }

    try {
      // Inject into current page
      const result = await controlTab.cdp.Runtime.evaluate({
        expression: bundleScript,
        awaitPromise: false,
        returnByValue: false,
      });

      if (result.exceptionDetails) {
        console.error("Bundle injection error:", result.exceptionDetails);
        throw new Error(
          `Bundle injection failed: ${result.exceptionDetails.text}`
        );
      }

      console.log(`✅ Bundle ${bundleName} injected successfully`);
    } catch (error) {
      console.error(`❌ Failed to inject bundle ${bundleName}:`, error);
      throw error;
    }
  }

  /**
   * Handle dynamic bundle injection based on client purpose
   */
  async handleClientPurposeChange(clientPurposes) {
    if (!this.controlTabInjected) {
      console.log(
        "Control tab not yet injected, skipping dynamic bundle injection"
      );
      return;
    }

    // Check if captcha detector is needed but not yet injected
    const needsCaptchaDetector = clientPurposes.includes("captcha");

    if (needsCaptchaDetector) {
      console.log(
        "🔍 Captcha client detected, injecting captcha detector bundle..."
      );
      try {
        await this.injectBundleIntoControlTab("captcha-detector-bundle.min.js");
      } catch (error) {
        console.error("Failed to inject captcha detector bundle:", error);
      }
    }
  }

  /**
   * Inject streamer script into a target tab
   */
  async injectTargetTabScript(tabId, signalingServerUrl) {
    const targetTab = this.browserManager.getTargetTab(tabId);
    if (!targetTab) {
      throw new Error(`Target tab not found: ${tabId}`);
    }

    console.log(`💉 Injecting streamer script into tab: ${tabId}`);

    // Use the new external streamer script
    const script = await this.getTargetTabStreamerScript(
      signalingServerUrl,
      tabId
    );

    // Use the new external streamer script
    const autoScript = await this.getTargetTabStreamerScriptInitializer(
      signalingServerUrl,
      tabId
    );

    try {
      // Method 1: Inject into current page
      const result = await targetTab.cdp.Runtime.evaluate({
        expression: script,
        awaitPromise: false,
        returnByValue: false,
      });

      if (result.exceptionDetails) {
        console.error("Script injection error:", result.exceptionDetails);
        throw new Error(
          `Script injection failed: ${result.exceptionDetails.text}`
        );
      }

      // Method 2: Try to add script for new document loads (persistence)
      let persistentResult = null;
      try {
        persistentResult =
          await targetTab.cdp.Page.addScriptToEvaluateOnNewDocument({
            source: wrapForMainFrameOnly(autoScript),
          });
        console.log(`✅ Persistent script added for tab: ${tabId}`);
      } catch (persistentError) {
        console.log(
          `⚠️  Persistent script not supported for tab: ${tabId}, using immediate injection only ${persistentError}`
        );
      }

      // Store script IDs for cleanup
      this.injectedScripts.set(tabId, {
        isInjected: true,
        timestamp: Date.now(),
      });

      if (persistentResult && persistentResult.identifier) {
        this.persistentScripts.set(tabId, persistentResult.identifier);
      }

      targetTab.isInjected = true;

      console.log(`✅ Script injected successfully into tab: ${tabId}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to inject script into tab ${tabId}:`, error);
      throw error;
    }
  }

  /**
   * Remove injected script from a target tab
   */
  async removeScript(tabId) {
    const targetTab = this.browserManager.getTargetTab(tabId);
    if (!targetTab) {
      console.warn(`Target tab not found for script removal: ${tabId}`);
      return;
    }

    console.log(`🗑️ Removing script from tab: ${tabId}`);

    try {
      // Remove persistent script
      const persistentScriptId = this.persistentScripts.get(tabId);
      if (persistentScriptId) {
        await targetTab.cdp.Runtime.removeScriptToEvaluateOnNewDocument({
          identifier: persistentScriptId,
        });
        this.persistentScripts.delete(tabId);
      }

      // Clean up tracking
      this.injectedScripts.delete(tabId);
      targetTab.isInjected = false;

      console.log(`✅ Script removed from tab: ${tabId}`);
    } catch (error) {
      console.error(`❌ Failed to remove script from tab ${tabId}:`, error);
    }
  }

  /**
   * Check if script is injected in a tab
   */
  isScriptInjected(tabId) {
    return this.injectedScripts.has(tabId);
  }

  /**
   * Get all tabs with injected scripts
   */
  getInjectedTabs() {
    return Array.from(this.injectedScripts.keys());
  }

  /**
   * Cleanup all injected scripts
   */
  async cleanup() {
    console.log("🧹 Cleaning up script injector...");

    const tabIds = Array.from(this.injectedScripts.keys());
    for (const tabId of tabIds) {
      await this.removeScript(tabId);
    }

    console.log("✅ Script injector cleanup complete");
  }
}
