// Global Window Extensions
declare global {
  interface Window {
    InterceptorPipeline?: new (
      interceptors?: BaseInterceptorInterface[],
      controller?: InterceptorPipelineInterface
    ) => InterceptorPipelineInterface;
    BaseInterceptor?: new (
      name: string,
      defaultConfig?: InterceptorConfig
    ) => BaseInterceptorInterface;
    VideoFrameInterceptor?: new (
      name?: string,
      options?: VideoCropConfig
    ) => BaseInterceptorInterface;
    BrightnessInterceptor?: new (
      name?: string,
      options?: BrightnessConfig
    ) => BaseInterceptorInterface;
    BlurInterceptor?: new (
      name?: string,
      options?: BlurConfig
    ) => BaseInterceptorInterface;
    ChangeDetectorInterceptor?: new (
      name?: string,
      options?: ChangeDetectorConfig
    ) => BaseInterceptorInterface;
    controlTabManager?: import("../control/control-tab-manager").default;
    tabStreamer?: import("../target/tab-streamer").default;
  }
}

// CaptchaDetectorCallback
export type CaptchaDetectorCallback = (
  rgbaBuffer: Uint8Array,
  dimensions: { width: number; height: number }
) => Promise<void>;

/**
 * Cross Tab Communicator interface - handles communication between tabs
 * Exposed on window.crossTabCommunicator
 */
export interface CrossTabCommunicator {
  /**
   * Send a message to another tab
   */
  sendMessage(type: string, data?: any): Promise<any>;

  /**
   * Set up message listener
   */
  onMessage(callback: (message: any) => void): void;

  /**
   * Clean up resources
   */
  cleanup(): void;
}

export interface InterceptorConfig {
  debug?: boolean;
  enabled?: boolean;
  [key: string]: any;
}

export interface BrightnessConfig extends InterceptorConfig {
  brightness?: number;
}

export interface BlurConfig extends InterceptorConfig {
  blurRadius?: number;
}

export interface InterceptorStats {
  framesProcessed: number;
  errorsEncountered: number;
  averageProcessingTime: number;
  lastProcessingTime: number;
  totalProcessingTime: number;
}

export interface BaseInterceptorInterface {
  readonly name: string;
  readonly type: string;
  readonly isInitialized: boolean;
  readonly isEnabled: boolean;
  readonly config: InterceptorConfig;
  readonly stats: InterceptorStats;

  initialize(videoTrack?: MediaStreamTrack): Promise<MediaStreamTrack>;
  processFrame(
    frame: VideoFrame,
    controller: TransformStreamDefaultController<VideoFrame>
  ): Promise<void>;
  processVideoFrame(frame: VideoFrame): Promise<VideoFrame>;
  updateConfig(newConfig: Partial<InterceptorConfig>): void;
  cleanup(): Promise<void>;
  enable(): void;
  disable(): void;
  getStats(): InterceptorStats;
  log(...args: any[]): void;
}

// Specific Interceptor Configs
export interface VideoCropConfig extends InterceptorConfig {
  enableCropping?: boolean;
  cropRegion?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  defaultCropRegion?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  frameRate?: number;
}

export interface ChangeDetectorConfig extends InterceptorConfig {
  changeThreshold?: number;
  stabilityThreshold?: number;
  consecutiveStableFrames?: number;
  maxWaitDuration?: number;
  comparisonInterval?: number;
  pixelSampling?: number;
}

export interface InterceptorConstructor {
  new (name: string, config?: InterceptorConfig): BaseInterceptorInterface;
}

export type InterceptorRegistryInterface =
  import("../interceptors/interceptor-registry").InterceptorRegistry;

export type InterceptorPipelineInterface =
  import("../interceptors/interceptor-pipeline").default;
