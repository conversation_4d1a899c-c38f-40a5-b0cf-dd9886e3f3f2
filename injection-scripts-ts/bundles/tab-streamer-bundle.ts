import TabStreamer from '../target/tab-streamer';

// Initialize TabStreamer if not already done
if (!window.tabStreamer) {
  console.log('[tab-streamer-bundle] Injecting tab streamer...');
  // Initialize the target tab streamer
  const signalingServerUrl = '${KAKU_WS_ENDPOINT}';
  const tabId = '${TAB_ID}';
  const autoInitialize = '${AUTO_INITIALIZE}';
  window.tabStreamer = new TabStreamer(signalingServerUrl, tabId, autoInitialize);

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    if (window.tabStreamer) {
      window.tabStreamer.cleanup();
    }
  });
}
