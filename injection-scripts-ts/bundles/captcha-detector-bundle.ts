import VideoFrameInterceptor from '../interceptors/video-crop-interceptor';
import ChangeDetectorInterceptor from '../interceptors/change-detector-interceptor';

// Register captcha detector interceptors
if (window.controlTabManager) {
  // Register VideoFrameInterceptor (video-crop)
  console.log('[captcha-detector-bundle] Registering video-crop interceptor...');
  window.controlTabManager.interceptorRegistry.register('video-crop', VideoFrameInterceptor, {
    debug: true,
    enabled: true,
    enableCropping: true,
    cropRegion: {
      x: 0,
      y: 0,
      width: window.innerWidth,
      height: window.innerHeight,
    },
  });
  console.log('[captcha-detector-bundle] Video-crop interceptor registered');
  window.controlTabManager.interceptorRegistry.register(
    'change-detector',
    ChangeDetectorInterceptor,
    {
      debug: true,
      enabled: false,
    },
  );
  console.log('[captcha-detector-bundle] Change-detector interceptor registered');
} else {
  console.error('[captcha-detector-bundle] controlTabManager does not exist');
}
