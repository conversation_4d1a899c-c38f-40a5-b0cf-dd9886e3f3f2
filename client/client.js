/**
 * POC Browser Streaming Web Client
 *
 * Connects to signaling server and displays multiple streams in grid layout
 * Handles WebRTC connections and stream management
 */

class StreamingClient {
  constructor() {
    this.signalingServerUrl = "ws://localhost:8080";
    this.websocket = null;
    this.isConnected = false;
    this.clientId = null;
    this.purpose = "screen-stream"; // Default purpose

    // WebRTC configuration
    this.rtcConfig = {
      iceServers: [
        { urls: "stun:stun.cloudflare.com:3478" },
        { urls: "stun:stun.l.google.com:19302" },
      ],
      iceCandidatePoolSize: 10,
    };

    // Single-stream state management
    this.availableTabs = new Map(); // tabId -> tabInfo
    this.currentStream = null; // { peerConnection, stream, tabId }
    this.currentTabId = null;
    this.dataChannel = null; // WebRTC data channel for user events

    // Touch gesture state
    this.isDragging = false;
    this.lastDragY = 0;

    // UI elements
    this.connectBtn = document.getElementById("connectBtn");
    this.disconnectBtn = document.getElementById("disconnectBtn");
    this.statusIndicator = document.getElementById("statusIndicator");
    this.statusText = document.getElementById("statusText");
    this.tabsGrid = document.getElementById("tabsGrid");
    this.currentStreamVideo = document.getElementById("currentStreamVideo");
    this.streamInfo = document.getElementById("streamInfo");
    this.logsContainer = document.getElementById("logsContainer");

    // Multi-interceptor control elements
    this.videoCropEnabled = document.getElementById("videoCropEnabled");
    this.cropRegionInput = document.getElementById("cropRegionInput");
    this.applyCropBtn = document.getElementById("applyCropBtn");

    this.brightnessEnabled = document.getElementById("brightnessEnabled");
    this.brightnessSlider = document.getElementById("brightnessSlider");
    this.brightnessValue = document.getElementById("brightnessValue");

    this.blurEnabled = document.getElementById("blurEnabled");
    this.blurSlider = document.getElementById("blurSlider");
    this.blurValue = document.getElementById("blurValue");

    this.applyPipelineBtn = document.getElementById("applyPipelineBtn");
    this.resetPipelineBtn = document.getElementById("resetPipelineBtn");
    this.pipelineStatus = document.getElementById("pipelineStatus");

    // Multi-interceptor state
    this.pipelineConfig = {
      interceptorNames: [],
      interceptorConfigs: {
        "video-crop": {
          enabled: false,
          enableCropping: false,
          cropRegion: null,
        },
        "brightness-filter": {
          enabled: false,
          brightness: 1.0,
        },
        "blur-effect": {
          enabled: false,
          blurRadius: 0,
        },
      },
    };

    // Legacy compatibility
    this.interceptorEnabled = false;
    this.enableCropping = false;
    this.currentCropRegion = null;

    this.init();
  }

  init() {
    this.log("info", "Initializing streaming client...");
    this.setupEventListeners();
    this.updateUI();

    // Initialize multi-interceptor UI
    this.updateBrightnessValue();
    this.updateBlurValue();
    this.updatePipelineUI();
  }

  setupEventListeners() {
    this.connectBtn.addEventListener("click", () => this.connect());
    this.disconnectBtn.addEventListener("click", () => this.disconnect());

    // Purpose selector event listener
    this.purposeSelect = document.getElementById("purposeSelect");
    this.purposeSelect.addEventListener("change", () => {
      this.purpose = this.purposeSelect.value;
      this.log("info", `Client purpose changed to: ${this.purpose}`);
    });

    // Multi-interceptor control event listeners
    this.videoCropEnabled.addEventListener("change", () =>
      this.updatePipelineFromUI()
    );
    this.applyCropBtn.addEventListener("click", () => this.applyCropRegion());

    this.brightnessEnabled.addEventListener("change", () =>
      this.updatePipelineFromUI()
    );
    this.brightnessSlider.addEventListener("input", () =>
      this.updateBrightnessValue()
    );
    this.brightnessSlider.addEventListener("change", () =>
      this.updatePipelineFromUI()
    );

    this.blurEnabled.addEventListener("change", () =>
      this.updatePipelineFromUI()
    );
    this.blurSlider.addEventListener("input", () => this.updateBlurValue());
    this.blurSlider.addEventListener("change", () =>
      this.updatePipelineFromUI()
    );

    this.applyPipelineBtn.addEventListener("click", () => this.applyPipeline());
    this.resetPipelineBtn.addEventListener("click", () => this.resetPipeline());

    // Handle Enter key in crop region input
    this.cropRegionInput.addEventListener("keypress", (event) => {
      if (event.key === "Enter") {
        this.applyCropRegion();
      }
    });

    // Handle page unload
    window.addEventListener("beforeunload", () => {
      this.disconnect();
    });
  }

  async connect() {
    if (this.isConnected) {
      this.log("warn", "Already connected to signaling server");
      return;
    }

    this.log("info", "Connecting to signaling server...");
    this.updateStatus("connecting", "Connecting...");

    try {
      this.websocket = new WebSocket(this.signalingServerUrl);

      this.websocket.onopen = () => {
        this.log("success", "Connected to signaling server");
        this.isConnected = true;
        this.updateStatus("connected", "Connected");
        this.updateUI();
      };

      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          this.log(
            "error",
            `Failed to parse message: ${error.message} -> ${event.data}`
          );
        }
      };

      this.websocket.onclose = () => {
        this.log("warn", "Disconnected from signaling server");
        this.isConnected = false;
        this.clientId = null;
        this.updateStatus("disconnected", "Disconnected");
        this.updateUI();
        this.clearStreams();
      };

      this.websocket.onerror = (error) => {
        this.log(
          "error",
          `WebSocket error: ${error.message || "Unknown error"}`
        );
        this.updateStatus("error", "Connection Error");
      };
    } catch (error) {
      this.log("error", `Failed to connect: ${error.message}`);
      this.updateStatus("error", "Connection Failed");
    }
  }

  disconnect() {
    if (!this.isConnected) return;

    this.log("info", "Disconnecting from signaling server...");

    // Close all peer connections
    this.clearStreams();

    // Close WebSocket
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }

    this.isConnected = false;
    this.clientId = null;
    this.updateStatus("disconnected", "Disconnected");
    this.updateUI();
  }

  handleMessage(message) {
    this.log("info", `Received: ${message.type}`);

    switch (message.type) {
      case "welcome":
        this.handleWelcome(message);
        break;
      case "available-streams":
        this.handleAvailableStreams(message);
        break;
      case "available-tabs-updated":
        this.handleAvailableTabsUpdated(message);
        break;
      case "target-tab-available":
        this.handleTargetTabAvailable(message);
        break;
      case "target-tab-unavailable":
        this.handleTargetTabUnavailable(message);
        break;
      case "target-tab-updated":
        this.handleTargetTabUpdated(message);
        break;
      case "stream-ready":
        this.handleStreamReady(message);
        break;
      case "stream-ended":
        this.handleStreamEnded(message);
        break;
      case "webrtc-offer":
        this.handleWebRTCOffer(message);
        break;
      case "webrtc-answer":
        this.handleWebRTCAnswer(message);
        break;
      case "webrtc-ice-candidate":
        this.handleWebRTCIceCandidate(message);
        break;
      case "interceptor-status":
        this.handleInterceptorStatus(message);
        break;
      case "pipeline-status":
        this.handlePipelineStatus(message);
        break;
      case "pipeline-status-to-web-client":
        this.handlePipelineStatus(message);
        break;
      case "stream-paused":
        this.handleStreamPaused(message);
        break;
      case "stream-resumed":
        this.handleStreamResumed(message);
        break;
      case "error":
        this.log("error", `Server error: ${message.message}`);
        break;
      default:
        this.log("warn", `Unknown message type: ${message.type}`);
    }
  }

  handleWelcome(message) {
    this.clientId = message.clientId;
    this.log("success", `Registered with client ID: ${this.clientId}`);

    // Get current purpose from selector
    this.purpose = this.purposeSelect.value;

    // Register as web client with purpose
    this.sendMessage({
      type: "register-web-client",
      purpose: this.purpose,
      metadata: {
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
      },
    });

    this.log("info", `Registered with purpose: ${this.purpose}`);
  }

  handleAvailableStreams(message) {
    this.log("info", `Received ${message.targetTabs.length} available tabs`);

    // Update available tabs
    this.availableTabs.clear();
    message.targetTabs.forEach((tab) => {
      this.availableTabs.set(tab.tabId, tab);
    });

    this.updateTabsGrid();
  }

  handleAvailableTabsUpdated(message) {
    this.log("info", `Available tabs updated: ${message.tabs.length} tabs`);

    // Update available tabs for single-stream mode
    this.availableTabs.clear();
    message.tabs.forEach((tab) => {
      this.availableTabs.set(tab.tabId, tab);
    });

    this.updateTabsGrid();
  }

  handleTargetTabAvailable(message) {
    this.log("info", `New tab available: ${message.title}`);
    this.availableTabs.set(message.tabId, message);
    this.updateTabsGrid();
  }

  handleTargetTabUnavailable(message) {
    this.log("warn", `Tab unavailable: ${message.tabId}`);
    this.availableTabs.delete(message.tabId);
    this.updateTabsGrid();

    // Stop any active streams for this tab
    if (this.currentStream) {
      this.stopCurrentStream();
    }
  }

  handleTargetTabUpdated(message) {
    this.log("info", `Tab updated: ${message.title}`);
    if (this.availableTabs.has(message.tabId)) {
      const existingTab = this.availableTabs.get(message.tabId);
      this.availableTabs.set(message.tabId, { ...existingTab, ...message });
      this.updateTabsGrid();
    }
  }

  handleStreamReady(message) {
    this.log("success", `Stream ready for tab: ${message.tabId}`);

    // For single-stream mode, just update the stream info
    const tabInfo = this.availableTabs.get(message.tabId);
    if (tabInfo) {
      this.updateStreamInfo(
        `🟢 Streaming from: ${tabInfo.title || "Unknown Tab"}`
      );
      this.log("info", `Single stream ready for tab: ${message.tabId}`);
    } else {
      this.log("warn", `No tab info found for tabId: ${message.tabId}`);
    }
  }

  handleStreamEnded(message) {
    this.log("warn", `Stream ended for tab: ${message.tabId}`);
    this.stopCurrentStream();
  }

  async handleWebRTCOffer(message) {
    this.log("info", `Received WebRTC offer for tab: ${message.tabId}`);

    try {
      // For single-stream mode, stop any existing stream
      if (this.currentStream) {
        this.currentStream.peerConnection.close();
        this.currentStream = null;
      }

      // Create peer connection
      const peerConnection = new RTCPeerConnection(this.rtcConfig);

      // Setup data channel for user events
      peerConnection.ondatachannel = (event) => {
        this.dataChannel = event.channel;
        this.setupDataChannelHandlers();
      };

      // Setup event handlers
      peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          console.log("candidate");
          this.sendMessage({
            type: "webrtc-ice-candidate-from-web-client",
            candidate: event.candidate,
            tabId: message.tabId,
          });
        }
      };

      peerConnection.ontrack = (event) => {
        this.log("success", `Received media track for tab: ${message.tabId}`);
        console.log("[WebClient] ontrack event:", event);

        const [stream] = event.streams;
        console.log("[WebClient] stream:", stream);

        // Display stream in single video element
        this.displaySingleStream(stream, message.tabId);
      };

      peerConnection.onconnectionstatechange = () => {
        this.log("info", `Connection state: ${peerConnection.connectionState}`);
        if (peerConnection.connectionState === "failed") {
          this.log(
            "error",
            `WebRTC connection failed for tab: ${message.tabId}`
          );
          this.stopCurrentStream();
        }
      };

      // Set remote description
      await peerConnection.setRemoteDescription(message.offer);

      // Create answer
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // Send answer
      console.log("sending answer");
      this.sendMessage({
        type: "webrtc-answer-from-web-client",
        answer: answer,
        tabId: message.tabId,
      });

      // Store as current stream
      this.currentStream = {
        peerConnection,
        stream: null,
        tabId: message.tabId,
      };
    } catch (error) {
      this.log("error", `Failed to handle WebRTC offer: ${error.message}`);
    }
  }

  async handleWebRTCAnswer(message) {
    this.log("info", `Received WebRTC answer for tab: ${message.tabId}`);

    const streamData = this.currentStream;
    if (streamData && streamData.peerConnection) {
      try {
        await streamData.peerConnection.setRemoteDescription(message.answer);
      } catch (error) {
        this.log("error", `Failed to set remote description: ${error.message}`);
      }
    }
  }

  async handleWebRTCIceCandidate(message) {
    const streamData = this.currentStream;
    if (streamData && streamData.peerConnection) {
      try {
        await streamData.peerConnection.addIceCandidate(
          new RTCIceCandidate(message.candidate)
        );
      } catch (error) {
        this.log("error", `Failed to add ICE candidate: ${error.message}`);
      }
    }
  }

  displayStream(tabId, mediaStream, tabInfo) {
    this.log("success", `Displaying stream for tab: ${tabId}`);
    console.log("[WebClient] displayStream called with:", {
      tabId,
      mediaStream,
      tabInfo,
    });
    console.log(
      "[WebClient] mediaStream.getTracks():",
      mediaStream.getTracks()
    );

    // Update stream data
    const streamData = this.currentStream;
    if (streamData) {
      streamData.stream = mediaStream;
    }

    // Only create UI if it doesn't already exist
    const existingContainer = document.getElementById(`stream-${tabId}`);
    if (!existingContainer) {
      this.addStreamToUI(tabId, mediaStream, tabInfo);
    } else {
      // Update existing video element with new stream
      const video = existingContainer.querySelector(".stream-video");
      if (video) {
        video.srcObject = mediaStream;
      }
    }
  }

  addStreamToUI(tabId, mediaStream, tabInfo) {
    // Remove empty state if present
    const emptyState = this.streamsGrid.querySelector(".empty-state");
    if (emptyState) {
      emptyState.remove();
    }

    const streamContainer = document.createElement("div");
    streamContainer.className = "stream-container";
    streamContainer.id = `stream-${tabId}`;

    streamContainer.innerHTML = `
      <div class="stream-header">
        📺 ${tabInfo.title || "Unknown Tab"}
      </div>
      <div class="stream-content">
        <video class="stream-video" autoplay muted playsinline></video>
      </div>
      <div class="stream-controls">
        <div class="stream-info">
          <div>${tabInfo.url || "Unknown URL"}</div>
          <div>Tab ID: ${tabId.substring(0, 8)}...</div>
        </div>
        <button class="btn btn-secondary btn-small" onclick="client.stopStream('${tabId}')">
          Stop Stream
        </button>
      </div>
    `;

    const video = streamContainer.querySelector(".stream-video");
    console.log("[WebClient] Setting video.srcObject:", { video, mediaStream });
    video.srcObject = mediaStream;

    // Add event listeners to video element for debugging
    video.onloadedmetadata = () => {
      console.log(
        "[WebClient] Video metadata loaded:",
        video.videoWidth,
        "x",
        video.videoHeight
      );
    };
    video.onplay = () => {
      console.log("[WebClient] Video started playing");
    };
    video.onerror = (error) => {
      console.error("[WebClient] Video error:", error);
    };

    this.streamsGrid.appendChild(streamContainer);
  }

  removeStreamFromUI(tabId) {
    const streamElement = document.getElementById(`stream-${tabId}`);
    if (streamElement) {
      streamElement.remove();
    }

    // Add empty state if no streams left
    if (this.streamsGrid.children.length === 0) {
      this.streamsGrid.innerHTML = `
        <div class="empty-state">
          <h3>No active streams</h3>
          <p>Click "Start Stream" on any available tab to begin streaming</p>
        </div>
      `;
    }
  }

  updateTabsGrid() {
    if (this.availableTabs.size === 0) {
      this.tabsGrid.innerHTML = `
        <div class="empty-state">
          <h3>No target tabs available</h3>
          <p>Connect to the signaling server to see available tabs</p>
        </div>
      `;
      return;
    }

    this.tabsGrid.innerHTML = "";

    for (const [tabId, tabInfo] of this.availableTabs) {
      const isCurrentStream = this.currentTabId === tabId;

      const tabCard = document.createElement("div");
      tabCard.className = `tab-card ${isCurrentStream ? "streaming" : ""}`;

      // Make tab clickable for single-stream mode
      if (!isCurrentStream) {
        tabCard.style.cursor = "pointer";
        tabCard.addEventListener("click", () => this.requestStream(tabId));
      }

      tabCard.innerHTML = `
        <div class="tab-title">${tabInfo.title || "Unknown Tab"}</div>
        <div class="tab-url">${tabInfo.url || "Unknown URL"}</div>
        <div class="tab-status">
          ${isCurrentStream ? "🟢 Currently Streaming" : "🔵 Click to Stream"}
        </div>
      `;

      this.tabsGrid.appendChild(tabCard);
    }
  }

  /**
   * Request single stream for a tab
   */
  requestStream(tabId) {
    const tabInfo = this.availableTabs.get(tabId);
    if (!tabInfo) {
      this.log("error", `Tab not found: ${tabId}`);
      return;
    }

    this.log("info", `Requesting stream for: ${tabInfo.title}`);

    // Send request to signaling server
    this.sendMessage({
      type: "request-stream",
      tabId: tabId,
    });

    // Update current tab
    this.currentTabId = tabId;
    this.updateTabsGrid();
  }

  /**
   * Update stream info display
   */
  updateStreamInfo(message) {
    if (this.streamInfo) {
      this.streamInfo.innerHTML = `<p>${message}</p>`;
    }
  }

  /**
   * Display single stream in video element
   */
  displaySingleStream(stream, tabId) {
    if (this.currentStreamVideo) {
      this.currentStreamVideo.srcObject = stream;
      this.log("success", `Stream displayed for tab: ${tabId}`);

      // Update stream info
      const tabInfo = this.availableTabs.get(this.currentTabId);
      if (tabInfo) {
        this.updateStreamInfo(
          `🟢 Streaming from: ${tabInfo.title || "Unknown Tab"}`
        );
      }

      // Add click event listener for user event replay
      this.setupVideoClickHandler(tabId);

      // Request pipeline status for this tab
      this.requestPipelineStatus(tabId);

      // Update interceptor UI
      this.updateInterceptorUI();
    }
  }

  /**
   * Stop current stream
   */
  stopCurrentStream() {
    if (this.currentStream) {
      this.currentStream.peerConnection.close();
      this.currentStream = null;
    }

    if (this.currentStreamVideo) {
      this.currentStreamVideo.srcObject = null;
    }

    this.currentTabId = null;
    this.updateStreamInfo(
      "No stream active. Click on a tab above to start streaming."
    );
    this.updateTabsGrid();

    // Reset interceptor UI when stream stops
    this.updateInterceptorUI();
  }

  clearStreams() {
    // Stop current stream
    this.stopCurrentStream();
  }

  updateStatus(status, text) {
    this.statusText.textContent = text;
    this.statusIndicator.className = `status-indicator ${
      status === "connected" ? "connected" : ""
    }`;
  }

  updateUI() {
    this.connectBtn.disabled = this.isConnected;
    this.disconnectBtn.disabled = !this.isConnected;
  }

  sendMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message));
    } else {
      this.log("warn", "Cannot send message - not connected");
    }
  }

  log(level, message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement("div");
    logEntry.className = "log-entry";
    logEntry.innerHTML = `
      <span class="log-timestamp">[${timestamp}]</span>
      <span class="log-level-${level}">[${level.toUpperCase()}]</span>
      ${message}
    `;

    this.logsContainer.appendChild(logEntry);
    this.logsContainer.scrollTop = this.logsContainer.scrollHeight;

    // Keep only last 100 log entries
    while (this.logsContainer.children.length > 100) {
      this.logsContainer.removeChild(this.logsContainer.firstChild);
    }

    console.log(`[${level.toUpperCase()}] ${message}`);
  }

  generateId() {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }

  /**
   * Setup data channel handlers for user event transmission
   */
  setupDataChannelHandlers() {
    if (!this.dataChannel) return;

    this.dataChannel.onopen = () => {
      console.log("[WebClient] Data channel opened for user events");
      this.log("success", "User event replay enabled");
    };

    this.dataChannel.onclose = () => {
      console.log("[WebClient] Data channel closed");
      this.log("info", "User event replay disabled");
    };

    this.dataChannel.onerror = (error) => {
      console.error("[WebClient] Data channel error:", error);
      this.log("error", "User event replay error");
    };

    this.dataChannel.onmessage = (event) => {
      console.log("[WebClient] Received data channel message:", event.data);
    };
  }

  /**
   * Setup event handlers on video element
   */
  setupVideoClickHandler(tabId) {
    if (!this.currentStreamVideo) return;

    // Remove existing event handlers
    this.currentStreamVideo.removeEventListener(
      "click",
      this.videoClickHandler
    );
    this.currentStreamVideo.removeEventListener(
      "keydown",
      this.videoKeydownHandler
    );
    this.currentStreamVideo.removeEventListener(
      "keyup",
      this.videoKeyupHandler
    );
    this.currentStreamVideo.removeEventListener(
      "keypress",
      this.videoKeypressHandler
    );
    this.currentStreamVideo.removeEventListener(
      "wheel",
      this.videoWheelHandler
    );
    this.currentStreamVideo.removeEventListener(
      "touchstart",
      this.videoTouchStartHandler
    );
    this.currentStreamVideo.removeEventListener(
      "touchmove",
      this.videoTouchMoveHandler
    );
    this.currentStreamVideo.removeEventListener(
      "touchend",
      this.videoTouchEndHandler
    );

    // Create new event handlers
    this.videoClickHandler = (event) => {
      this.handleVideoClick(event, tabId);
    };

    this.videoKeydownHandler = (event) => {
      this.handleVideoKeydown(event, tabId);
    };

    this.videoKeyupHandler = (event) => {
      this.handleVideoKeyup(event, tabId);
    };

    this.videoKeypressHandler = (event) => {
      this.handleVideoKeypress(event, tabId);
    };

    this.videoWheelHandler = (event) => {
      this.handleVideoWheel(event, tabId);
    };

    this.videoTouchStartHandler = (event) => {
      this.handleVideoTouchStart(event, tabId);
    };

    this.videoTouchMoveHandler = (event) => {
      this.handleVideoTouchMove(event, tabId);
    };

    this.videoTouchEndHandler = (event) => {
      this.handleVideoTouchEnd(event, tabId);
    };

    // Add event handlers
    this.currentStreamVideo.addEventListener("click", this.videoClickHandler);
    this.currentStreamVideo.addEventListener(
      "keydown",
      this.videoKeydownHandler
    );
    this.currentStreamVideo.addEventListener("keyup", this.videoKeyupHandler);
    this.currentStreamVideo.addEventListener(
      "keypress",
      this.videoKeypressHandler
    );
    this.currentStreamVideo.addEventListener("wheel", this.videoWheelHandler);
    this.currentStreamVideo.addEventListener(
      "touchstart",
      this.videoTouchStartHandler
    );
    this.currentStreamVideo.addEventListener(
      "touchmove",
      this.videoTouchMoveHandler
    );
    this.currentStreamVideo.addEventListener(
      "touchend",
      this.videoTouchEndHandler
    );

    // Make video focusable for keyboard events
    this.currentStreamVideo.setAttribute("tabindex", "0");

    console.log("[WebClient] Video event handlers setup for tab:", tabId);
  }

  /**
   * Handle click events on video element
   */
  handleVideoClick(event, tabId) {
    if (!this.dataChannel || this.dataChannel.readyState !== "open") {
      console.warn("[WebClient] Data channel not available for user events");
      return;
    }

    // Get click coordinates relative to video element
    const rect = this.currentStreamVideo.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Get video dimensions
    const videoWidth = this.currentStreamVideo.videoWidth;
    const videoHeight = this.currentStreamVideo.videoHeight;
    const displayWidth = rect.width;
    const displayHeight = rect.height;

    // Calculate normalized coordinates (0-1)
    const normalizedX = x / displayWidth;
    const normalizedY = y / displayHeight;

    // Create user event message
    const userEvent = {
      type: "user-event",
      eventType: "click",
      x: normalizedX,
      y: normalizedY,
      tabId: tabId,
      timestamp: Date.now(),
      videoWidth: videoWidth,
      videoHeight: videoHeight,
      displayWidth: displayWidth,
      displayHeight: displayHeight,
    };

    // Send through data channel
    try {
      this.dataChannel.send(JSON.stringify(userEvent));
      console.log("[WebClient] Sent user event:", userEvent);
      this.log(
        "info",
        `Click sent: (${Math.round(normalizedX * 100)}%, ${Math.round(
          normalizedY * 100
        )}%)`
      );
    } catch (error) {
      console.error("[WebClient] Failed to send user event:", error);
      this.log("error", "Failed to send click event");
    }
  }

  /**
   * Handle keydown events on video element
   */
  handleVideoKeydown(event, tabId) {
    this.sendKeyboardEvent(event, tabId, "keydown");
  }

  /**
   * Handle keyup events on video element
   */
  handleVideoKeyup(event, tabId) {
    this.sendKeyboardEvent(event, tabId, "keyup");
  }

  /**
   * Handle keypress events on video element
   */
  handleVideoKeypress(event, tabId) {
    this.sendKeyboardEvent(event, tabId, "keypress");
  }

  /**
   * Handle wheel events on video element, scrolling support
   */
  handleVideoWheel(event, tabId) {
    this.sendScrollEvent(event, tabId);
  }

  /**
   * Send keyboard event through data channel
   */
  sendKeyboardEvent(event, tabId, eventType) {
    if (!this.dataChannel || this.dataChannel.readyState !== "open") {
      console.warn("[WebClient] Data channel not available for user events");
      return;
    }

    if (eventType === "keydown" && this.shouldPreventDefault(event)) {
      event.preventDefault();
    }

    const userEvent = {
      type: "user-event",
      eventType: eventType,
      key: event.key,
      code: event.code,
      keyCode: event.keyCode,
      text: eventType === "keypress" ? event.key : undefined,
      tabId: tabId,
      timestamp: Date.now(),
    };

    // Send through data channel
    try {
      this.dataChannel.send(JSON.stringify(userEvent));
      console.log("[WebClient] Sent keyboard event:", userEvent);
      this.log("info", `${eventType} sent: ${event.key} (${event.code})`);
    } catch (error) {
      console.error("[WebClient] Failed to send keyboard event:", error);
      this.log("error", `Failed to send ${eventType} event`);
    }
  }

  /**
   * Send scroll event through data channel
   */
  sendScrollEvent(event, tabId) {
    if (!this.dataChannel || this.dataChannel.readyState !== "open") {
      console.warn("[WebClient] Data channel not available for user events");
      return;
    }

    event.preventDefault();

    // Get scroll coordinates relative to video element
    const rect = this.currentStreamVideo.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Get video dimensions
    const videoWidth = this.currentStreamVideo.videoWidth;
    const videoHeight = this.currentStreamVideo.videoHeight;
    const displayWidth = rect.width;
    const displayHeight = rect.height;

    // Calculate normalized coordinates (0-1)
    const normalizedX = x / displayWidth;
    const normalizedY = y / displayHeight;

    // Create user event message
    const userEvent = {
      type: "user-event",
      eventType: "scroll",
      x: normalizedX,
      y: normalizedY,
      deltaX: event.deltaX,
      deltaY: event.deltaY,
      tabId: tabId,
      timestamp: Date.now(),
      videoWidth: videoWidth,
      videoHeight: videoHeight,
      displayWidth: displayWidth,
      displayHeight: displayHeight,
    };

    // Send through data channel
    try {
      this.dataChannel.send(JSON.stringify(userEvent));
      console.log("[WebClient] Sent scroll event:", userEvent);
      this.log(
        "info",
        `Scroll sent: deltaX=${event.deltaX}, deltaY=${event.deltaY}`
      );
    } catch (error) {
      console.error("[WebClient] Failed to send scroll event:", error);
      this.log("error", "Failed to send scroll event");
    }
  }

  handleVideoTouchStart(event, tabId) {
    if (event.touches.length !== 1) return;

    event.preventDefault();
    this.isDragging = true;
    this.lastDragY = event.touches[0].clientY;
  }

  handleVideoTouchMove(event, tabId) {
    if (!this.isDragging || event.touches.length !== 1) return;

    event.preventDefault();

    const currentY = event.touches[0].clientY;
    const deltaY = currentY - this.lastDragY;
    this.lastDragY = currentY;

    if (Math.abs(deltaY) < 1) return;

    const rect = this.currentStreamVideo.getBoundingClientRect();
    const x = event.touches[0].clientX - rect.left;
    const y = event.touches[0].clientY - rect.top;

    const normalizedX = x / rect.width;
    const normalizedY = y / rect.height;

    const userEvent = {
      type: "user-event",
      eventType: "scrollGesture",
      x: normalizedX,
      y: normalizedY,
      deltaY: deltaY,
      tabId: tabId,
      timestamp: Date.now(),
      videoWidth: this.currentStreamVideo.videoWidth,
      videoHeight: this.currentStreamVideo.videoHeight,
      displayWidth: rect.width,
      displayHeight: rect.height,
    };

    if (this.dataChannel && this.dataChannel.readyState === "open") {
      try {
        this.dataChannel.send(JSON.stringify(userEvent));
      } catch (error) {
        console.error(
          "[WebClient] Failed to send scroll gesture event:",
          error
        );
      }
    }
  }

  handleVideoTouchEnd(event, tabId) {
    this.isDragging = false;
    this.lastDragY = 0;
  }

  /**
   * Determine if we should prevent default browser behavior
   */
  shouldPreventDefault(event) {
    // Prevent default for navigation keys, function keys, etc.
    const preventKeys = [
      "ArrowUp",
      "ArrowDown",
      "ArrowLeft",
      "ArrowRight",
      "PageUp",
      "PageDown",
      "Home",
      "End",
      "F1",
      "F2",
      "F3",
      "F4",
      "F5",
      "F6",
      "F7",
      "F8",
      "F9",
      "F10",
      "F11",
      "F12",
    ];
    return preventKeys.includes(event.key) || event.key.startsWith("F");
  }

  /**
   * Handle interceptor status updates from control tab
   */
  handleInterceptorStatus(message) {
    this.log("info", "Received interceptor status update");

    this.interceptorEnabled = message.enabled || false;
    this.enableCropping = message.enableCropping || false;
    this.currentCropRegion = message.cropRegion || null;

    this.updateInterceptorUI();
  }

  /**
   * Handle pipeline status updates from control tab
   */
  handlePipelineStatus(message) {
    this.log("info", "Received pipeline status update");

    // Update pipeline configuration from server
    if (message.interceptorNames) {
      this.pipelineConfig.interceptorNames = message.interceptorNames;
    }
    if (message.interceptorConfigs) {
      this.pipelineConfig.interceptorConfigs = {
        ...this.pipelineConfig.interceptorConfigs,
        ...message.interceptorConfigs,
      };
    }

    // Update UI controls to match server state
    this.videoCropEnabled.checked =
      this.pipelineConfig.interceptorConfigs["video-crop"]?.enabled || false;
    this.brightnessEnabled.checked =
      this.pipelineConfig.interceptorConfigs["brightness-filter"]?.enabled ||
      false;
    this.blurEnabled.checked =
      this.pipelineConfig.interceptorConfigs["blur-effect"]?.enabled || false;

    // Update slider values
    if (
      this.pipelineConfig.interceptorConfigs["brightness-filter"]
        ?.brightness !== undefined
    ) {
      this.brightnessSlider.value =
        this.pipelineConfig.interceptorConfigs["brightness-filter"].brightness;
      this.updateBrightnessValue();
    }
    if (
      this.pipelineConfig.interceptorConfigs["blur-effect"]?.blurRadius !==
      undefined
    ) {
      this.blurSlider.value =
        this.pipelineConfig.interceptorConfigs["blur-effect"].blurRadius;
      this.updateBlurValue();
    }

    // Update crop region if provided
    if (this.pipelineConfig.interceptorConfigs["video-crop"]?.cropRegion) {
      const { x, y, width, height } =
        this.pipelineConfig.interceptorConfigs["video-crop"].cropRegion;
      this.cropRegionInput.value = `${x},${y},${width},${height}`;
    }

    // Update UI visual state
    this.updatePipelineUI();
  }

  /**
   * Handle stream paused notification from control tab
   */
  handleStreamPaused(message) {
    this.log("warn", `Stream paused: ${message.reason}`);

    // Show progress indicator
    this.showStreamPauseIndicator(message);
  }

  /**
   * Handle stream resumed notification from control tab
   */
  handleStreamResumed(message) {
    this.log(
      "success",
      `Stream resumed: ${message.reason} (paused for ${message.pauseDuration}ms)`
    );

    // Hide progress indicator
    this.hideStreamPauseIndicator();
  }

  /**
   * Show stream pause indicator in the UI
   */
  showStreamPauseIndicator(pauseInfo) {
    // Remove any existing indicator
    this.hideStreamPauseIndicator();

    // Create pause indicator
    const indicator = document.createElement("div");
    indicator.id = "stream-pause-indicator";
    indicator.className = "stream-pause-indicator";
    indicator.innerHTML = `
      <div class="pause-content">
        <div class="pause-indicator">
          <div class="pause-icon">⏸️</div>
          <div class="pause-spinner"></div>
        </div>
        <div class="pause-text">Waiting for screen stability...</div>
      </div>
    `;

    // Find the current video element to overlay on
    const currentVideo = document.getElementById("currentStreamVideo");

    if (currentVideo && currentVideo.parentElement) {
      // Make the video container relative if it isn't already
      const videoContainer = currentVideo.parentElement;
      if (getComputedStyle(videoContainer).position === "static") {
        videoContainer.style.position = "relative";
      }

      // Add indicator as overlay on the video container
      videoContainer.appendChild(indicator);
    } else {
      // Fallback: add to body if video not found
      document.body.appendChild(indicator);
    }
  }

  /**
   * Hide stream pause indicator
   */
  hideStreamPauseIndicator() {
    const indicator = document.getElementById("stream-pause-indicator");
    if (indicator) {
      indicator.remove();
    }
  }

  /**
   * Toggle cropping functionality
   */
  toggleCropping() {
    if (!this.currentTabId) {
      this.log("warn", "No active stream to control");
      this.croppingToggle.checked = this.enableCropping; // Reset toggle
      return;
    }

    const newState = this.croppingToggle.checked;
    this.log("info", `${newState ? "Enabling" : "Disabling"} cropping`);

    this.sendMessage({
      type: "toggle-interceptor-cropping",
      tabId: this.currentTabId,
      enabled: newState,
    });
  }

  /**
   * Apply crop region from input field
   */
  applyCropRegion() {
    if (!this.currentTabId) {
      this.log("warn", "No active stream to control");
      return;
    }

    const cropInput = this.cropRegionInput.value.trim();
    if (!cropInput) {
      this.log("warn", "Please enter a crop region (x,y,width,height)");
      return;
    }

    // Parse crop region input
    const parts = cropInput.split(",").map((part) => parseInt(part.trim()));
    if (parts.length !== 4 || parts.some(isNaN)) {
      this.log(
        "error",
        "Invalid crop region format. Use: x,y,width,height (e.g., 100,100,400,300)"
      );
      return;
    }

    const [x, y, width, height] = parts;
    const cropRegion = { x, y, width, height };

    this.log("info", `Applying crop region: ${JSON.stringify(cropRegion)}`);

    // Update pipeline configuration
    this.pipelineConfig.interceptorConfigs["video-crop"].cropRegion =
      cropRegion;
    this.pipelineConfig.interceptorConfigs["video-crop"].enableCropping = true;

    // Send updated pipeline configuration
    this.sendMessage({
      type: "configure-interceptor-pipeline",
      tabId: this.currentTabId,
      interceptorNames: this.pipelineConfig.interceptorNames,
      interceptorConfigs: this.pipelineConfig.interceptorConfigs,
    });
  }

  /**
   * Request interceptor status from control tab
   */
  requestInterceptorStatus(tabId) {
    this.log("info", "Requesting interceptor status");
    this.sendMessage({
      type: "request-interceptor-status",
      tabId: tabId,
    });
  }

  /**
   * Request pipeline status from control tab
   */
  requestPipelineStatus(tabId) {
    this.log("info", "Requesting pipeline status");
    this.sendMessage({
      type: "request-pipeline-status",
      tabId: tabId,
    });
  }

  /**
   * Update interceptor UI elements
   */
  updateInterceptorUI() {
    // Update cropping toggle
    this.croppingToggle.checked = this.enableCropping;

    // Update cropping status badge
    this.croppingStatus.textContent = this.enableCropping
      ? "Enabled"
      : "Disabled";
    this.croppingStatus.className = `status-badge ${
      this.enableCropping ? "enabled" : "disabled"
    }`;

    // Update crop region input
    if (this.currentCropRegion) {
      const { x, y, width, height } = this.currentCropRegion;
      this.cropRegionInput.value = `${x},${y},${width},${height}`;
    }

    // Update interceptor status
    const statusContainer = this.interceptorStatus;
    const hasActiveStream = !!this.currentTabId;

    if (hasActiveStream) {
      statusContainer.innerHTML = `
        <strong>Interceptor Status:</strong>
        <span class="status-badge ${
          this.interceptorEnabled ? "enabled" : "disabled"
        }">
          ${this.interceptorEnabled ? "Active" : "Inactive"}
        </span>
        <div style="margin-top: 0.5rem; font-size: 0.8rem;">
          Cropping: ${this.enableCropping ? "Enabled" : "Disabled"}
          ${
            this.currentCropRegion
              ? ` | Region: ${this.currentCropRegion.x},${this.currentCropRegion.y},${this.currentCropRegion.width},${this.currentCropRegion.height}`
              : ""
          }
        </div>
      `;
    } else {
      statusContainer.innerHTML = `
        <strong>Interceptor Status:</strong>
        <span class="status-badge disabled">Not Connected</span>
        <div style="margin-top: 0.5rem; font-size: 0.8rem;">
          Connect to a stream to control video frame processing
        </div>
      `;
    }

    // Enable/disable controls based on connection state
    this.videoCropEnabled.disabled = !hasActiveStream;
    this.applyCropBtn.disabled = !hasActiveStream;
    this.cropRegionInput.disabled = !hasActiveStream;
    this.brightnessEnabled.disabled = !hasActiveStream;
    this.brightnessSlider.disabled = !hasActiveStream;
    this.blurEnabled.disabled = !hasActiveStream;
    this.blurSlider.disabled = !hasActiveStream;
    this.applyPipelineBtn.disabled = !hasActiveStream;
  }

  /**
   * Update brightness value display
   */
  updateBrightnessValue() {
    const value = parseFloat(this.brightnessSlider.value);
    this.brightnessValue.textContent = value.toFixed(1);
  }

  /**
   * Update blur value display
   */
  updateBlurValue() {
    const value = parseInt(this.blurSlider.value);
    this.blurValue.textContent = `${value}px`;
  }

  /**
   * Update pipeline configuration from UI controls
   */
  updatePipelineFromUI() {
    // Update interceptor configurations from UI
    this.pipelineConfig.interceptorConfigs["video-crop"].enabled =
      this.videoCropEnabled.checked;
    this.pipelineConfig.interceptorConfigs["brightness-filter"].enabled =
      this.brightnessEnabled.checked;
    this.pipelineConfig.interceptorConfigs["brightness-filter"].brightness =
      parseFloat(this.brightnessSlider.value);
    this.pipelineConfig.interceptorConfigs["blur-effect"].enabled =
      this.blurEnabled.checked;
    this.pipelineConfig.interceptorConfigs["blur-effect"].blurRadius = parseInt(
      this.blurSlider.value
    );

    // Update interceptor names array based on enabled interceptors
    this.pipelineConfig.interceptorNames = [];
    if (this.pipelineConfig.interceptorConfigs["brightness-filter"].enabled) {
      this.pipelineConfig.interceptorNames.push("brightness-filter");
    }
    if (this.pipelineConfig.interceptorConfigs["blur-effect"].enabled) {
      this.pipelineConfig.interceptorNames.push("blur-effect");
    }
    if (this.pipelineConfig.interceptorConfigs["video-crop"].enabled) {
      this.pipelineConfig.interceptorNames.push("video-crop");
    }

    // Update UI visual state
    this.updatePipelineUI();
  }

  /**
   * Apply the current pipeline configuration
   */
  applyPipeline() {
    if (!this.currentTabId) {
      this.log("warn", "No active stream to configure");
      return;
    }

    this.log(
      "info",
      `Applying pipeline: ${this.pipelineConfig.interceptorNames.join(" → ")}`
    );

    // Send pipeline configuration to control tab
    this.sendMessage({
      type: "configure-interceptor-pipeline",
      tabId: this.currentTabId,
      interceptorNames: this.pipelineConfig.interceptorNames,
      interceptorConfigs: this.pipelineConfig.interceptorConfigs,
    });
  }

  /**
   * Reset pipeline to default configuration
   */
  resetPipeline() {
    this.log("info", "Resetting pipeline to defaults");

    // Reset UI controls
    this.videoCropEnabled.checked = false;
    this.brightnessEnabled.checked = false;
    this.brightnessSlider.value = "1.0";
    this.blurEnabled.checked = false;
    this.blurSlider.value = "0";

    // Update value displays
    this.updateBrightnessValue();
    this.updateBlurValue();

    // Update pipeline configuration
    this.updatePipelineFromUI();

    // Apply the reset configuration if there's an active stream
    if (this.currentTabId) {
      this.applyPipeline();
    }
  }

  /**
   * Update pipeline UI visual state
   */
  updatePipelineUI() {
    // Update interceptor item visual states
    const videoCropItem = document.getElementById("videoCropInterceptor");
    const brightnessItem = document.getElementById("brightnessInterceptor");
    const blurItem = document.getElementById("blurInterceptor");

    // Update visual states
    videoCropItem.className = `interceptor-item ${
      this.videoCropEnabled.checked ? "enabled" : "disabled"
    }`;
    brightnessItem.className = `interceptor-item ${
      this.brightnessEnabled.checked ? "enabled" : "disabled"
    }`;
    blurItem.className = `interceptor-item ${
      this.blurEnabled.checked ? "enabled" : "disabled"
    }`;

    // Update pipeline status
    const hasActiveStream = !!this.currentTabId;
    const enabledCount = this.pipelineConfig.interceptorNames.length;

    if (hasActiveStream) {
      this.pipelineStatus.innerHTML = `
        <strong>Pipeline Status:</strong>
        <span class="status-badge ${enabledCount > 0 ? "enabled" : "disabled"}">
          ${enabledCount > 0 ? `${enabledCount} Active` : "Inactive"}
        </span>
        <div style="margin-top: 0.5rem; font-size: 0.8rem;">
          ${
            enabledCount > 0
              ? `Processing: ${this.pipelineConfig.interceptorNames.join(
                  " → "
                )}`
              : "No interceptors enabled"
          }
        </div>
      `;
    } else {
      this.pipelineStatus.innerHTML = `
        <strong>Pipeline Status:</strong>
        <span class="status-badge disabled">Not Connected</span>
        <div style="margin-top: 0.5rem; font-size: 0.8rem;">
          Connect to a stream to configure video processing pipeline
        </div>
      `;
    }
  }
}

// Initialize the client
const client = new StreamingClient();
