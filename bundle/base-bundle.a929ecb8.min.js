!function(){"use strict";class e{constructor(e=[]){this.isInitialized=!1,this.isEnabled=!0,this.processedTrack=null,this.processor=null,this.generator=null,this.transformStream=null,this.updateInterceptorConfigs=e=>{if(e&&"object"==typeof e){this.log("info","Updating interceptor configurations:",e);for(const n of this.interceptors){const o=e[n.name];if(o)try{n.updateConfig(o),this.log("info",`Updated config for interceptor: ${n.name}`)}catch(t){this.log("error",`Failed to update config for interceptor ${n.name}:`,t)}}}else this.log("warn","Invalid interceptor configurations provided")},this.interceptors=[...e],this.isInitialized=!1,this.isEnabled=!0,this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0,interceptorStats:new Map},this.log("InterceptorPipeline created with",this.interceptors.length,"interceptors")}async initialize(e){if(this.isInitialized)return this.log("Pipeline already initialized, returning existing track"),this.processedTrack;this.log("Initializing pipeline with video track");try{return this.processor=new MediaStreamTrackProcessor({track:e}),this.generator=new MediaStreamTrackGenerator({kind:"video"}),this.transformStream=new TransformStream({transform:this.processFrame.bind(this)}),this.processor.readable.pipeThrough(this.transformStream).pipeTo(this.generator.writable).catch(e=>{this.log("Pipeline error:",e),this.stats.errorsEncountered++}),this.processedTrack=this.generator,this.isInitialized=!0,this.log("Pipeline initialized successfully"),this.processedTrack}catch(t){throw this.log("Error initializing pipeline:",t),this.stats.errorsEncountered++,t}}async processFrame(e,t){const n=performance.now();try{if(this.stats.framesProcessed++,!this.isEnabled||0===this.interceptors.length)return void t.enqueue(e);if(null==e)return;let r=e;const i=[];for(let t=0;t<this.interceptors.length;t++){const n=this.interceptors[t];if(n&&n.config.enabled){console.log("Processing frame through interceptor:",n.name);try{const t=performance.now(),o=await n.processVideoFrame(r),a=performance.now()-t;this.updateInterceptorStats(n.name,a),o!==r&&r!==e&&i.push(r),r=o}catch(o){this.log(`Error in interceptor ${n.name}:`,o),this.stats.errorsEncountered++}}}if(t.enqueue(r),i.forEach(e=>{try{e.close()}catch(o){}}),r!==e)try{e.close()}catch(o){}const a=performance.now()-n;this.stats.lastProcessingTime=a,this.stats.totalProcessingTime+=a,this.stats.averageProcessingTime=this.stats.totalProcessingTime/this.stats.framesProcessed}catch(o){this.log("Error processing frame:",o),this.stats.errorsEncountered++,t.enqueue(e)}}updateInterceptorStats(e,t){let n=this.stats.interceptorStats.get(e);n||(n={framesProcessed:0,totalProcessingTime:0,averageProcessingTime:0},this.stats.interceptorStats.set(e,n)),n.framesProcessed++,n.totalProcessingTime+=t,n.averageProcessingTime=n.totalProcessingTime/n.framesProcessed}addInterceptor(e){this.interceptors.push(e),this.log(`Added interceptor ${e.name} at end of pipeline`)}removeInterceptor(e){const t=this.interceptors.findIndex(t=>t.name===e);if(-1!==t){const e=this.interceptors.splice(t,1)[0];return this.log(`Removed interceptor ${e.name} from pipeline`),!0}return this.log(`Interceptor ${e} not found in pipeline`),!1}getInterceptor(e){return this.interceptors.find(t=>t.name===e)||null}enable(){this.isEnabled=!0,this.log("Pipeline enabled")}disable(){this.isEnabled=!1,this.log("Pipeline disabled")}updateInterceptorConfig(e,t){const n=this.getInterceptor(e);if(!n)return this.log(`Interceptor ${e} not found`),!1;try{return n.updateConfig(t),this.log(`Updated config for interceptor: ${e}`),!0}catch(o){return this.log(`Failed to update config for interceptor ${e}:`,o),!1}}setEnabled(e){this.isEnabled=e,this.log("Pipeline "+(e?"enabled":"disabled"))}setInterceptorEnabled(e,t){const n=this.getInterceptor(e);n?(n.config.enabled=t,this.log(`Interceptor ${e} ${t?"enabled":"disabled"}`)):this.log(`Interceptor ${e} not found`)}getStats(){return{...this.stats}}resetStats(){this.stats.framesProcessed=0,this.stats.errorsEncountered=0,this.stats.averageProcessingTime=0,this.stats.lastProcessingTime=0,this.stats.totalProcessingTime=0,this.stats.interceptorStats.clear(),this.log("Pipeline statistics reset")}async cleanup(){if(this.log("Cleaning up pipeline resources"),this.transformStream){try{await this.transformStream.writable.close()}catch(e){}this.transformStream=null}this.processor&&(this.processor=null),this.generator&&(this.generator=null),this.processedTrack=null,this.isInitialized=!1,this.log("Pipeline cleanup completed")}log(...e){console.log("[InterceptorPipeline]",...e)}}class t{constructor(){this.interceptorClasses=new Map,this.defaultConfigs=new Map,this.clientInterceptors=new Map,this.clientConfigs=new Map,this.log("InterceptorRegistry initialized")}register(e,t,n={}){if(!e||"string"!=typeof e)throw new Error("Interceptor name must be a non-empty string");if(!t||"function"!=typeof t)throw new Error("Interceptor class must be a constructor function");this.interceptorClasses.set(e,t),this.defaultConfigs.set(e,n),this.log(`Registered interceptor: ${e}`)}unregister(e){this.interceptorClasses.has(e)&&(this.interceptorClasses.delete(e),this.defaultConfigs.delete(e),this.log(`Unregistered interceptor: ${e}`))}getRegisteredInterceptors(){return Array.from(this.interceptorClasses.keys())}isRegistered(e){return this.interceptorClasses.has(e)}create(e,t){const n=this.interceptorClasses.get(e);if(!n)return this.log(`Warning: Interceptor class not found for ${e}`),null;const o={...this.defaultConfigs.get(e)||{},...t};try{return new n(e,o)}catch(r){return this.log(`Error creating interceptor ${e}:`,r),null}}setClientConfiguration(e,t=[],n={}){for(const r of t)if(!this.interceptorClasses.has(r))throw new Error(`Unknown interceptor: ${r}`);const o={interceptorNames:[...t],configs:new Map};for(const r of t){const e=this.defaultConfigs.get(r)||{},t=n[r]||{};o.configs.set(r,{...e,...t})}this.clientConfigs.set(e,o),this.log(`Set configuration for client ${e}:`,t)}getClientConfiguration(e){const t=this.clientConfigs.get(e);return t?{interceptorNames:[...t.interceptorNames],configs:new Map(t.configs)}:{interceptorNames:[],configs:new Map}}createClientInterceptors(e){if(this.clientInterceptors.has(e)){this.log(`Interceptors already exist for client ${e}, returning existing instances`);const t=this.clientInterceptors.get(e);return Array.from(t.values())}const t=this.getClientConfiguration(e),n=[],o=new Map;try{for(const r of t.interceptorNames){const i=this.interceptorClasses.get(r),a=t.configs.get(r)||{};if(!i){this.log(`Warning: Interceptor class not found for ${r}, skipping`);continue}const s=new i(r,a);n.push(s),o.set(r,s),this.log(`Created ${r} interceptor for client ${e}`)}return this.clientInterceptors.set(e,o),n}catch(r){for(const e of n)try{e.cleanup()}catch(i){this.log("Error cleaning up interceptor during creation failure:",i)}throw new Error(`Failed to create interceptors for client ${e}: ${r instanceof Error?r.message:String(r)}`)}}getClientInterceptors(e){const t=this.clientInterceptors.get(e);if(!t)return[];return this.getClientConfiguration(e).interceptorNames.map(e=>t.get(e)).filter(e=>void 0!==e)}updateClientInterceptorConfig(e,t,n){const o=this.clientConfigs.get(e);if(!o)throw new Error(`No configuration found for client: ${e}`);const r=o.configs.get(t)||{};o.configs.set(t,{...r,...n});const i=this.clientInterceptors.get(e);if(i&&i.has(t)){i.get(t).updateConfig(n)}this.log(`Updated ${t} config for client ${e}`)}cleanupClientInterceptors(e){const t=this.clientInterceptors.get(e);if(t){for(const[o,r]of t)try{r.cleanup(),this.log(`Cleaned up ${o} interceptor for client ${e}`)}catch(n){this.log(`Error cleaning up ${o} interceptor:`,n)}this.clientInterceptors.delete(e)}this.clientConfigs.delete(e),this.log(`Cleaned up all interceptors for client ${e}`)}getStats(){const e={registeredInterceptors:this.interceptorClasses.size,activeClients:this.clientInterceptors.size,totalActiveInterceptors:0,clientStats:{}};for(const[t,n]of this.clientInterceptors){const o=Array.from(n.values());e.totalActiveInterceptors+=o.length,e.clientStats[t]={interceptorCount:o.length,interceptors:o.map(e=>({name:e.name,type:e.type,isEnabled:e.isEnabled,stats:e.getStats()}))}}return e}log(...e){console.log("[InterceptorRegistry]",...e)}}class n{constructor(e={}){this.mainCdpClient=null,this.options={wsEndpoint:e.wsEndpoint,debug:e.debug||!1,timeout:e.timeout||3e4,retryAttempts:e.retryAttempts||3,...e},this.connections=new Map,this.eventHandlers=new Map,this.log("[CDP-Manager] Initialized"),this.initializeMainCDPClient(this.options.wsEndpoint)}log(e,...t){this.options.debug&&console.log(`[CDP-Manager] ${e}`,...t)}async initializeMainCDPClient(e){if(!this.mainCdpClient)try{const{CDP:t}=await function(e){function t(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return Promise.resolve().then(n=>{for(const e of n||[])"rejected"===e.status&&t(e.reason);return e().catch(t)})}(async()=>{const{CDP:e}=await Promise.resolve().then(()=>W);return{CDP:e}});this.mainCdpClient=new t({webSocketDebuggerUrl:e}),await this.mainCdpClient.Target.setAutoAttach({autoAttach:!0,flatten:!0,waitForDebuggerOnStart:!1}),this.log("Main CDP client initialized successfully")}catch(t){throw this.log("Failed to initialize main CDP client:",t),t}}async addConnection(e){try{if(this.connections.has(e)){return this.connections.get(e)}await this.initializeMainCDPClient(this.options.wsEndpoint);const t=(await this.mainCdpClient.Target.attachToTarget({targetId:e,flatten:!0})).sessionId;await this.mainCdpClient.Runtime.enable(void 0,t);const n={client:this.mainCdpClient,sessionId:t,createdAt:Date.now(),isConnected:!0};return this.connections.set(e,n),this.log(`CDP connection established for tab: ${e} with session: ${t}`),n}catch(t){throw this.log(`Failed to add connection to tab ${e}:`,t),t}}async removeConnection(e){try{const n=this.connections.get(e);if(!n)return;if(this.mainCdpClient&&n.sessionId)try{await this.mainCdpClient.Target.detachFromTarget({sessionId:n.sessionId})}catch(t){this.log(`Failed to detach from target ${e}:`,t)}n.isConnected=!1,this.connections.delete(e),this.log(`CDP connection removed for tab: ${e}`)}catch(n){throw this.log(`Failed to remove connection from tab ${e}:`,n),n}}getConnection(e){return this.connections.get(e)||null}getAllConnections(){return new Map(this.connections)}async executeCommand(e,t,n={}){try{const o=this.connections.get(e);if(!o)throw new Error(`No connection found for tab: ${e}`);if(!o.isConnected)throw new Error(`Connection to tab ${e} is not active`);const[r,i]=t.split(".");if(!r||!i)throw new Error(`Invalid method format: ${t}. Expected format: Domain.method`);const a=o.client[r];if(!a)throw new Error(`Unsupported domain: ${r}`);const s=a[i];if("function"!=typeof s)throw new Error(`Unsupported method: ${i} in domain: ${r}`);return await s.call(a,n,o.sessionId)}catch(o){throw this.log(`Failed to execute ${t} on tab ${e}:`,o),o}}async getTargetTabInfo(e){try{const t=await this.executeCommand(e,"Runtime.evaluate",{expression:"({\n          width: window.innerWidth,\n          height: window.innerHeight,\n          url: window.location.href,\n          title: document.title\n        })",returnByValue:!0,awaitPromise:!0});return t.result.value}catch(t){return this.log(`Failed to get tab info for ${e}:`,t),{width:1920,height:1080,url:"unknown",title:"Unknown"}}}async executeScript(e,t){try{const n=await this.executeCommand(e,"Runtime.evaluate",{expression:t,returnByValue:!0});return n.result.value}catch(n){return this.log(`Failed to execute script on tab ${e}:`,n),null}}registerEventHandler(e,t){this.eventHandlers.set(e,t)}unregisterEventHandler(e){this.eventHandlers.delete(e)}async handleUserEvent(e,t){try{const n=this.connections.get(t);if(!n)throw new Error(`No CDP connection found for tab: ${t}`);if(!n.isConnected)throw new Error(`Connection to tab ${t} is not active`);const o=this.eventHandlers.get(e.eventType);if(!o)return void this.log(`No handler registered for event type: ${e.eventType}`);await o(e,t)}catch(n){throw this.log(`Failed to handle user event on tab ${t}:`,n),n}}async handleClickEvent(e,t){try{const n=await this.getTargetTabInfo(t);if(!n)throw new Error("Could not get target tab info");if("number"!=typeof e.x||"number"!=typeof e.y)throw new Error("Click event requires valid x and y coordinates");const o=e.x*n.width,r=e.y*n.height,i={type:"mousePressed",x:Math.round(o),y:Math.round(r),button:"left",clickCount:1,buttons:1};await this.executeCommand(t,"Input.dispatchMouseEvent",i),await new Promise(e=>setTimeout(e,50));const a={type:"mouseReleased",x:Math.round(o),y:Math.round(r),button:"left",clickCount:1,buttons:0};await this.executeCommand(t,"Input.dispatchMouseEvent",a)}catch(n){throw this.log(`Failed to handle click event on tab ${t}:`,n),n}}async handleScrollEvent(e,t){try{const n=await this.getTargetTabInfo(t);if(!n)throw new Error("Could not get target tab info");if("number"!=typeof e.x||"number"!=typeof e.y)throw new Error("Scroll event requires valid x and y coordinates");const o=e.x*n.width,r=e.y*n.height;await this.executeCommand(t,"Input.dispatchMouseEvent",{type:"mouseWheel",x:Math.round(o),y:Math.round(r),deltaX:e.deltaX||0,deltaY:e.deltaY||0})}catch(n){throw this.log(`Failed to handle scroll event on tab ${t}:`,n),n}}async handleKeyEvent(e,t){try{const n=this.mapBrowserEventTypeToCDP(e.eventType),o={type:n,key:e.key,code:e.code,keyCode:e.keyCode};"keyDown"===n&&e.text&&(o.text=e.text),await this.executeCommand(t,"Input.dispatchKeyEvent",o)}catch(n){throw this.log(`Failed to handle key event on tab ${t}:`,n),n}}async handleScrollGestureEvent(e,t){try{const n=await this.getTargetTabInfo(t);if(!n)throw new Error("Could not get target tab info");if("number"!=typeof e.x||"number"!=typeof e.y)throw new Error("Scroll gesture event requires valid x and y coordinates");if("number"!=typeof e.deltaY)throw new Error("Scroll gesture event requires valid deltaY");const o=e.x*n.width,r=e.y*n.height,i=-e.deltaY;await this.executeCommand(t,"Input.synthesizeScrollGesture",{x:Math.round(o),y:Math.round(r),yDistance:i,xDistance:0,speed:800,gestureSourceType:"touch"}),this.log(`Scroll gesture executed on tab ${t}: deltaY=${e.deltaY}, yDistance=${i}`)}catch(n){this.log(`Failed to handle scroll gesture on tab ${t}:`,n)}}mapBrowserEventTypeToCDP(e){return{keydown:"keyDown",keyup:"keyUp",keypress:"keyDown"}[e]||"keyDown"}initializeDefaultHandlers(){this.registerEventHandler("click",this.handleClickEvent.bind(this)),this.registerEventHandler("scroll",this.handleScrollEvent.bind(this)),this.registerEventHandler("scrollGesture",this.handleScrollGestureEvent.bind(this)),this.registerEventHandler("keydown",this.handleKeyEvent.bind(this)),this.registerEventHandler("keyup",this.handleKeyEvent.bind(this)),this.registerEventHandler("keypress",this.handleKeyEvent.bind(this))}async cleanup(){const e=Array.from(this.connections.keys()).map(e=>this.removeConnection(e));if(await Promise.all(e),this.mainCdpClient){try{this.mainCdpClient.connection.close()}catch(t){this.log("Error closing main CDP client:",t)}this.mainCdpClient=null}this.eventHandlers.clear(),this.log("CDP Manager cleanup completed")}getStats(){return{totalConnections:this.connections.size,eventHandlers:this.eventHandlers.size,connections:Array.from(this.connections.entries()).map(([e,t])=>({tabId:e,sessionId:t.sessionId,createdAt:t.createdAt}))}}}class o{constructor(){this.signalingServerUrl="${KAKU_WS_ENDPOINT}",this.wsBrowserEndpoint="${BROWSER_WS_ENDPOINT}",this.websocket=null,this.isConnected=!1,this.rtcConfig={iceServers:[{urls:"stun:stun.cloudflare.com:3478"},{urls:"stun:stun.l.google.com:19302"}],iceCandidatePoolSize:10},this.webClientGroups=new Map,this.tabGroups=new Map,this.targetConnections=new Map,this.interceptorRegistry=new t,this.cdpManager=null,this.init()}async init(){console.log("[control-tab-manager] Initializing control tab manager..."),this.initializeCDPManager(),await this.connectToSignalingServer(),this.setupPageListeners(),this.createControlTabUI()}initializeCDPManager(){var e;try{console.log("[control-tab-manager] Checking for CDPManager availability..."),console.log("[control-tab-manager] CDPManager found, initializing..."),this.cdpManager=new n({wsEndpoint:this.wsBrowserEndpoint,debug:!0}),null==(e=this.cdpManager)||e.initializeDefaultHandlers(),console.log("[control-tab-manager] CDP Manager initialized successfully"),console.log("[control-tab-manager] CDP Manager instance:",this.cdpManager)}catch(t){throw console.error("[control-tab-manager] Failed to initialize CDP Manager:",t),t}}initializePipeline(t){const n=this.webClientGroups.get(t);if(!n)throw console.warn("[control-tab-manager] No web client group found for:",t),new Error("No web client group found for "+t);try{const o=this.interceptorRegistry.createClientInterceptors(t),r=new e(o);return n.interceptorPipeline=r,setTimeout(()=>this.updateInterceptorDebugPanel(),100),r}catch(o){throw console.error("[control-tab-manager] Error initializing interceptors for client:",t,o),o}}getClientInterceptorConfig(e){return this.interceptorRegistry.getClientConfiguration(e)}processStreamForClient(e,t){const n=this.webClientGroups.get(t),o=this.getClientInterceptorConfig(t);if(!n)throw console.warn("Cannot process stream - No webClientGroup found for:",t),new Error("No webClientGroup found for "+t);if(!o)return e;if(0===o.interceptorNames.length)return e;try{const r=e.getVideoTracks()[0];if(!r)return e;let i=n.interceptorPipeline;if(!i&&(i=this.initializePipeline(t),!i))return console.warn("[control-tab-manager] Failed to initialize interceptors for client:",t),e;console.log("[control-tab-manager] Processing stream through interceptor pipeline for client:",t,"interceptors:",o.interceptorNames);const a=i.initialize(r),s=[a,...e.getAudioTracks()].filter(e=>e instanceof MediaStreamTrack);return new MediaStream(s)}catch(r){return console.error("[control-tab-manager] Error processing stream through interceptor pipeline for client:",t,r),e}}async connectToSignalingServer(){try{console.log("[control-tab-manager] Connecting to signaling server..."),this.websocket=new WebSocket(this.signalingServerUrl),this.websocket.onopen=()=>{console.log("[control-tab-manager] Control tab connected to signaling server"),this.isConnected=!0,this.sendMessage({type:"register-control-tab",metadata:{userAgent:navigator.userAgent,timestamp:Date.now()}})},this.websocket.onmessage=e=>{try{const t=JSON.parse(e.data);this.handleMessage(t)}catch(t){console.error("[control-tab-manager] Failed to parse message:",t)}},this.websocket.onclose=()=>{console.log("[control-tab-manager] Control tab disconnected from signaling server"),this.isConnected=!1,this.scheduleReconnect()},this.websocket.onerror=e=>{console.error("[control-tab-manager] Control tab WebSocket error:",e)}}catch(e){console.error("[control-tab-manager] Failed to connect to signaling server:",e),this.scheduleReconnect()}}scheduleReconnect(){console.log("[control-tab-manager] Scheduling reconnection in 5 seconds..."),setTimeout(()=>{this.connectToSignalingServer()},5e3)}sendMessage(e){this.websocket&&this.websocket.readyState===WebSocket.OPEN?this.websocket.send(JSON.stringify(e)):console.warn("[control-tab-manager] Cannot send message - not connected")}handleMessage(e){switch(console.log("[control-tab-manager] Control tab received message:",e.type),e.type){case"target-tabs-list":this.handleTargetTabsList(e);break;case"target-tab-registered":this.handleTargetTabRegistered(e);break;case"target-tab-disconnected":this.handleTargetTabDisconnected(e);break;case"stream-stopped":this.handleStreamStopped(e);break;case"webrtc-offer":this.handleWebRTCOffer();break;case"webrtc-answer":this.handleWebRTCAnswer(e);break;case"webrtc-ice-candidate":this.handleWebRTCIceCandidate(e);break;case"webrtc-offer-from-target":this.handleTargetTabOffer(e);break;case"webrtc-ice-candidate-from-target":this.handleTargetTabIceCandidate(e);break;case"webrtc-answer-from-web-client":this.handleWebClientAnswer(e);break;case"webrtc-ice-candidate-from-web-client":this.handleWebClientIceCandidate(e);break;case"web-client-registered":this.handleWebClientRegistered(e);break;case"web-client-disconnected":this.handleWebClientDisconnected(e);break;default:console.log("[control-tab-manager] Unknown message type:",e.type)}}handleTargetTabsList(e){console.log("[control-tab-manager] Received target tabs list:",e.targetTabs.length),this.tabGroups.clear(),e.targetTabs.forEach(e=>{this.tabGroups.set(e.tabId,{tabInfo:e,stream:null})})}handleTargetTabRegistered(e){console.log("[control-tab-manager] Target tab registered:",e.tabId),this.tabGroups.set(e.tabId,{tabInfo:e,stream:null})}handleTargetTabDisconnected(e){console.log("[control-tab-manager] Target tab disconnected:",e.tabId);const t=this.tabGroups.get(e.tabId);(null==t?void 0:t.stream)&&this.cleanupStream(e.tabId),this.tabGroups.delete(e.tabId)}handleWebClientRegistered(t){console.log("[control-tab-manager] Web client registered:",t.webClientId,"with purpose:",t.purpose),this.webClientGroups.set(t.webClientId,{RTCPeerConnection:null,interceptorPipeline:new e,dataChannel:null,webClient:{clientInfo:t.metadata||{},purpose:t.purpose||"screen-stream",currentTabId:null,connected:!0}}),this.configureInterceptorsForClient(t.webClientId,t.purpose),this.createPeerConnectionForWebClient(t.webClientId)}configureInterceptorsForClient(e,t){console.log(`[control-tab-manager] Configuring interceptors for client ${e} with purpose: ${t}`);let n=[],o={};if("captcha"===t)n=["video-crop","change-detector"],o={"video-crop":{enabled:!0,enableCropping:!0,cropRegion:{x:0,y:0,width:window.innerWidth/2,height:window.innerHeight/2}},"change-detector":{enabled:!0,changeThreshold:5,stabilityThreshold:1,consecutiveStableFrames:3,maxWaitDuration:5e3,comparisonInterval:100,pixelSampling:2}};else n=[],o={};try{this.interceptorRegistry.setClientConfiguration(e,n,o),console.log(`[control-tab-manager] Interceptor configuration set for client ${e}:`,n)}catch(r){console.error(`[control-tab-manager] Failed to configure interceptors for client ${e}:`,r)}}handleWebClientDisconnected(e){console.log("[control-tab-manager] Web client disconnected:",e.webClientId),this.cleanupWebClient(e.webClientId)}handleStreamStopped(e){console.log("[control-tab-manager] Stream stopped for tab:",e.targetTabId),this.cleanupStream(e.targetTabId)}async handleWebRTCOffer(){console.log("[control-tab-manager] Received WebRTC offer")}async handleWebRTCAnswer(e){console.log("[control-tab-manager] Received WebRTC answer for web client:",e.webClientId);const t=this.webClientGroups.get(e.webClientId),n=null==t?void 0:t.RTCPeerConnection;if(n)try{await n.setRemoteDescription(new RTCSessionDescription(e.answer)),console.log("[control-tab-manager] WebRTC connection established for web client:",e.webClientId)}catch(o){console.error("[control-tab-manager] Failed to set remote description:",o)}}async handleWebRTCIceCandidate(e){const t=this.webClientGroups.get(e.webClientId),n=null==t?void 0:t.RTCPeerConnection;if(n)try{await n.addIceCandidate(e.candidate)}catch(o){console.error("[control-tab-manager] Failed to add ICE candidate:",o)}}async handleTargetTabOffer(e){console.log("[control-tab-manager] Received WebRTC offer from target tab:",e);const t={targetTabId:e.targetTabId,status:"connecting"},n=new RTCPeerConnection(this.rtcConfig);this.targetConnections.set(e.targetTabId,n),n.ontrack=async n=>{console.log("[control-tab-manager] Target stream event:",n);const[o]=n.streams,r=this.tabGroups.get(e.targetTabId);r&&(r.stream=o),console.log(`[control-tab-manager] Stored active stream for tab ${e.targetTabId}`);try{console.log(`[control-tab-manager] Attempting to establish CDP connection for tab: ${e.targetTabId}`),console.log("[control-tab-manager] CDP Manager available:",!!this.cdpManager),this.cdpManager?(console.log(`[control-tab-manager] Adding CDP connection for tab: ${e.targetTabId}`),console.log(`[control-tab-manager] Creating CDP target info for tab: ${e.targetTabId}`),await this.cdpManager.addConnection(e.targetTabId),console.log(`[control-tab-manager] CDP connection established for tab: ${e.targetTabId}`)):console.warn("[control-tab-manager] CDP Manager not available - user events will not work")}catch(i){console.error(`[control-tab-manager] Failed to establish CDP connection for tab ${e.targetTabId}:`,i)}this.displayStreamInControlTab(e.targetTabId,o,t),this.broadcastStreamToAllClients(o,e.targetTabId)},n.onicecandidate=e=>{e.candidate&&this.sendMessage({type:"webrtc-ice-candidate-to-target",candidate:e.candidate,targetTabId:t.targetTabId})};try{await n.setRemoteDescription(new RTCSessionDescription(e.offer));const o=await n.createAnswer();await n.setLocalDescription(o),this.sendMessage({type:"webrtc-answer-to-target",answer:o,targetTabId:t.targetTabId}),console.log("[control-tab-manager] WebRTC answer sent to target tab")}catch(o){console.error("[control-tab-manager] Failed to handle target tab offer:",o)}}async handleTargetTabIceCandidate(e){const t=this.targetConnections.get(e.targetTabId);if(t)try{await t.addIceCandidate(e.candidate)}catch(n){console.error("[control-tab-manager] Failed to add target ICE candidate:",n)}}async handleWebClientAnswer(e){console.log("[control-tab-manager] Received WebRTC answer from web client:",e.webClientId);const t=this.webClientGroups.get(e.webClientId),n=null==t?void 0:t.RTCPeerConnection;if(n)try{await n.setRemoteDescription(new RTCSessionDescription(e.answer)),console.log("[control-tab-manager] WebRTC connection established with web client:",e.webClientId)}catch(o){console.error("[control-tab-manager] Failed to set remote description from web client:",o)}else console.warn("[control-tab-manager] No peer connection found for web client:",e.webClientId)}async handleWebClientIceCandidate(e){console.log("[control-tab-manager] Received ICE candidate from web client:",e.webClientId);const t=this.webClientGroups.get(e.webClientId),n=null==t?void 0:t.RTCPeerConnection;if(n)try{await n.addIceCandidate(e.candidate)}catch(o){console.error("[control-tab-manager] Failed to add ICE candidate from web client:",o)}else console.warn("[control-tab-manager] No peer connection found for web client:",e.webClientId)}createPeerConnectionForWebClient(e){console.log("[control-tab-manager] Creating peer connection for web client:",e);const t=new RTCPeerConnection(this.rtcConfig),n=t.createDataChannel("userEvents",{ordered:!0});this.setupDataChannelHandlers(n,e),t.onicecandidate=t=>{t.candidate&&this.sendMessage({type:"webrtc-ice-candidate-to-web-client",candidate:t.candidate,webClientId:e})};const o=this.webClientGroups.get(e);o&&(o.RTCPeerConnection=t,o.dataChannel=n),this.addExistingTracksToNewClient(e,t),console.log("[control-tab-manager] Peer connection created for web client:",e)}addExistingTracksToNewClient(e,t){console.log("[control-tab-manager] Checking for existing active streams for new client:",e);for(const[n,o]of this.tabGroups)if(o.stream){console.log(`[control-tab-manager] Found active stream for tab ${n}, adding tracks to new client ${e}`);const r=o.stream;if(r&&r.getTracks().length>0){const o=this.processStreamForClient(r,e),i=this.webClientGroups.get(e);(null==i?void 0:i.webClient)&&(i.webClient.currentTabId=n),o.getTracks().forEach(n=>{console.log(`[control-tab-manager] Adding existing ${n.kind} track to new client:`,e),t.addTrack(n,o)}),this.createOfferToWebClient(n,e),console.log(`[control-tab-manager] Added existing stream tracks from tab ${n} to new client ${e}`);break}}Array.from(this.tabGroups.values()).some(e=>e.stream)||console.log("[control-tab-manager] No existing active streams found for new client:",e)}cleanupWebClient(e){console.log("[control-tab-manager] Cleaning up web client:",e);const t=this.webClientGroups.get(e);t&&(t.RTCPeerConnection&&t.RTCPeerConnection.close(),t.dataChannel&&t.dataChannel.close()),this.webClientGroups.delete(e),console.log("[control-tab-manager] Web client cleanup completed:",e)}broadcastStreamToAllClients(e,t){console.log("[control-tab-manager] Broadcasting stream to all connected web clients for tab:",t);for(const[n,o]of this.webClientGroups){const r=o.RTCPeerConnection;if(!r)continue;console.log("[control-tab-manager] Processing web client:",n,"- Connection state:",r.connectionState),o.webClient&&(o.webClient.currentTabId=t);const i=this.processStreamForClient(e,n);i.getTracks().forEach(e=>{const t=r.getSenders().find(t=>t.track&&t.track.kind===e.kind);t?(console.log(`[control-tab-manager] Replacing ${e.kind} track for client:`,n),t.replaceTrack(e)):(console.log(`[control-tab-manager] Adding ${e.kind} track for client:`,n),r.addTrack(e,i))});const a=r.connectionState,s=r.signalingState;"connected"===a?console.log(`[control-tab-manager] Client ${n} already connected - tracks updated via existing connection`):"stable"!==s||"new"!==a&&"connecting"!==a?"stable"!==s?console.log(`[control-tab-manager] Skipping offer for client ${n} - negotiation in progress (signaling: ${s})`):(console.log(`[control-tab-manager] Creating offer for client ${n} (connection: ${a}, signaling: ${s})`),this.createOfferToWebClient(t,n)):(console.log(`[control-tab-manager] Creating new offer for client ${n} (connection: ${a}, signaling: ${s})`),this.createOfferToWebClient(t,n))}console.log("[control-tab-manager] Stream broadcast completed to",this.webClientGroups.size,"clients")}async createOfferToWebClient(e,t){const n=this.webClientGroups.get(t),o=null==n?void 0:n.RTCPeerConnection;if(!o)return void console.error("[control-tab-manager] No peer connection found for web client:",t);const r=o.connectionState,i=o.signalingState;console.log(`[control-tab-manager] Creating offer for client ${t} - Connection: ${r}, Signaling: ${i}`);try{const n=await o.createOffer();await o.setLocalDescription(n),this.sendMessage({type:"webrtc-offer-to-web-client",offer:n,targetClientId:t,tabId:e,fromClientId:this.clientId}),console.log(`[control-tab-manager] WebRTC offer sent to web client: ${t} (${r} -> negotiating)`)}catch(a){console.error(`[control-tab-manager] Failed to create offer to web client ${t}:`,a.message),console.error(`[control-tab-manager] Error context - Connection: ${r}, Signaling: ${i}`)}}cleanupStream(e){var t;console.log("[control-tab-manager] Cleaning up stream for tab:",e);const n=this.targetConnections.get(e);n&&(n.close(),this.targetConnections.delete(e));for(const[s,c]of this.webClientGroups)if((null==(t=c.webClient)?void 0:t.currentTabId)===e){c.webClient.currentTabId=null;const e=c.RTCPeerConnection;e&&e.getSenders().forEach(t=>{t.track&&e.removeTrack(t)})}const o=document.getElementById(`poc-stream-${e}`);o&&o.remove();const r=document.getElementById("poc-streams-container");r&&0===r.children.length&&(r.innerHTML='<div style="color: #666; font-style: italic;">No active streams</div>');const i=this.tabGroups.get(e);i&&(i.stream=null);try{this.cdpManager&&(this.cdpManager.removeConnection(e),console.log(`[control-tab-manager] CDP connection cleaned up for tab: ${e}`))}catch(a){console.error(`[control-tab-manager] Failed to cleanup CDP connection for tab ${e}:`,a)}}createControlTabUI(){const e=document.createElement("div");e.id="poc-control-panel",e.style.cssText="\n        position: fixed;\n        top: 20px;\n        right: 20px;\n        width: 400px;\n        max-height: 600px;\n        background: rgba(0, 0, 0, 0.9);\n        color: white;\n        border-radius: 8px;\n        padding: 16px;\n        z-index: 10000;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        font-size: 14px;\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n        overflow-y: auto;\n      ",e.innerHTML='\n        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">\n          <h3 style="margin: 0; color: #4CAF50;">POC Control Tab</h3>\n          <button id="poc-toggle-panel" style="background: none; border: 1px solid #666; color: white; padding: 4px 8px; border-radius: 4px; cursor: pointer;">−</button>\n        </div>\n        <div id="poc-panel-content">\n          <div style="margin-bottom: 12px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">Status:</div>\n            <div id="poc-connection-status" style="color: #ff9800;">Connecting...</div>\n          </div>\n          <div style="margin-bottom: 12px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">Active Streams:</div>\n            <div id="poc-streams-container" style="max-height: 300px; overflow-y: auto;">\n              <div style="color: #666; font-style: italic;">No active streams</div>\n            </div>\n          </div>\n          <div style="margin-bottom: 12px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">Target Tabs:</div>\n            <div id="poc-tabs-container" style="max-height: 200px; overflow-y: auto;">\n              <div style="color: #666; font-style: italic;">No target tabs</div>\n            </div>\n          </div>\n          <div style="margin-bottom: 12px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">Interceptor Debug Panel:</div>\n            <div id="poc-interceptor-debug-container" style="max-height: 400px; overflow-y: auto; border: 1px solid #444; border-radius: 4px; padding: 8px; background: rgba(255,255,255,0.05);">\n              <div style="color: #666; font-style: italic;">No interceptor data</div>\n            </div>\n            <button id="poc-refresh-interceptors" style="margin-top: 8px; background: #4CAF50; border: none; color: white; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">Refresh Interceptor Status</button>\n          </div>\n        </div>\n      ',document.body.appendChild(e);const t=document.getElementById("poc-toggle-panel"),n=document.getElementById("poc-panel-content");let o=!1;null==t||t.addEventListener("click",()=>{o=!o,n&&(n.style.display=o?"none":"block"),t&&(t.textContent=o?"+":"−"),e.style.height=o?"auto":""});const r=document.getElementById("poc-refresh-interceptors");r&&r.addEventListener("click",()=>{this.updateInterceptorDebugPanel()}),this.updateConnectionStatus("Connected"),this.updateInterceptorDebugPanel()}updateConnectionStatus(e){const t=document.getElementById("poc-connection-status");t&&(t.textContent=e,t.style.color="Connected"===e?"#4CAF50":"#ff9800")}displayStreamInControlTab(e,t,n){var o;console.log("[control-tab-manager] Displaying stream in control tab for tab:",e);const r=document.getElementById("poc-streams-container");if(!r)return void console.warn("[control-tab-manager] Streams container not found");const i=r.querySelector('[style*="font-style: italic"]');i&&(null==(o=i.textContent)?void 0:o.includes("No active streams"))&&i.remove();const a=document.createElement("div");a.id=`poc-stream-${e}`,a.style.cssText="\n        margin-bottom: 12px;\n        padding: 8px;\n        border: 1px solid #333;\n        border-radius: 4px;\n        background: rgba(255, 255, 255, 0.05);\n      ",a.innerHTML=`\n        <div style="font-weight: bold; margin-bottom: 8px; color: #4CAF50;">\n          📺 Tab ${e.substring(0,8)}...\n        </div>\n        <video\n          id="poc-video-${e}"\n          autoplay\n          muted\n          playsinline\n          style="width: 100%; height: 150px; background: #000; border-radius: 4px; object-fit: contain;"\n        ></video>\n        <div style="margin-top: 8px; font-size: 12px; color: #ccc;">\n          Target: ${n.targetTabId||"Unknown"}\n        </div>\n      `,r.appendChild(a);const s=document.getElementById(`poc-video-${e}`);s&&t&&(s.srcObject=t,s.onloadedmetadata=()=>{console.log("[control-tab-manager] Video metadata loaded in control tab")},s.onplay=()=>{console.log("[control-tab-manager] Video started playing in control tab")},s.onerror=e=>{console.error("[control-tab-manager] Video error in control tab:",e)})}setupPageListeners(){window.addEventListener("beforeunload",()=>{console.log("[control-tab-manager] Control tab unloading...");for(const[e,t]of this.tabGroups)t.stream&&this.cleanupStream(e);this.websocket&&this.websocket.close()})}setupDataChannelHandlers(e,t){console.log("[control-tab-manager] Setting up data channel for web client:",t),e.onopen=()=>{console.log("[control-tab-manager] Data channel opened for web client:",t)},e.onclose=()=>{console.log("[control-tab-manager] Data channel closed for web client:",t)},e.onerror=e=>{console.error("[control-tab-manager] Data channel error:",e)},e.onmessage=e=>{var n;try{const o=JSON.parse(e.data);console.log("[control-tab-manager] Received user event:",o);const r=this.webClientGroups.get(t),i=null==(n=null==r?void 0:r.webClient)?void 0:n.currentTabId;console.log("[control-tab-manager] Web client group:",r),console.log("[control-tab-manager] Target tab ID:",i),console.log("[control-tab-manager] CDP Manager available:",!!this.cdpManager),i?(console.log("[control-tab-manager] Handling user event for target tab:",i),this.handleUserEvent(t,o,i)):console.warn("[control-tab-manager] No target tab for user event from client:",t)}catch(o){console.error("[control-tab-manager] Failed to parse user event:",o)}}}async handleUserEvent(e,t,n){if("user-event"===t.type){"click"===t.eventType&&this.enableChangeDetectorForClient(e);try{await this.replayEventOnTargetTab(t,n)}catch(o){console.error("[control-tab-manager] Failed to replay event:",o)}}else console.warn("[control-tab-manager] Unknown event type:",t.type)}enableChangeDetectorForClient(e){console.log("[control-tab-manager] Enabling Change detector for client:",e,"due to click event");const t=this.webClientGroups.get(e);if(!t)return void console.warn("[control-tab-manager] No web client group found for:",e);const n=t.interceptorPipeline;if(!n)return void console.warn("[control-tab-manager] No interceptor pipeline found for client:",e);const o=n.getInterceptor("change-detector");o?(o.setControlTabManager(this),o.setTriggeringWebClient(e),o.updateConfig({enabled:!0}),console.log(`[control-tab-manager] Change detector interceptor enabled for client ${e}`)):console.warn(`[control-tab-manager] Change detector interceptor not found for client ${e}`)}async replayEventOnTargetTab(e,t){if(this.cdpManager)try{await this.cdpManager.handleUserEvent(e,t)}catch(n){throw console.error("[control-tab-manager] Failed to replay event via CDPManager:",n),n}else console.warn("[control-tab-manager] CDP Manager not available - cannot replay event")}toggleInterceptor(e){console.log("[control-tab-manager] Interceptor controls are now per-client. Use the web client interface.")}toggleCropping(e){console.log("[control-tab-manager] Cropping controls are now per-client. Use the web client interface.")}setCropRegion(e){console.log("[control-tab-manager] Crop region controls are now per-client. Use the web client interface.")}updateInterceptorDebugPanel(){var e,t;const n=document.getElementById("poc-interceptor-debug-container");if(n)try{let o="";if(o+='<div style="margin-bottom: 12px; padding: 8px; background: rgba(0,100,200,0.1); border-radius: 4px;">',o+='<div style="font-weight: bold; color: #64B5F6; margin-bottom: 4px;">📋 Interceptor Registry</div>',void 0!==this.interceptorRegistry){o+=`<div style="font-size: 12px;">Registered: ${Array.from(this.interceptorRegistry.interceptorClasses.keys()).join(", ")||"None"}</div>`,o+=`<div style="font-size: 12px;">Total Clients: ${this.interceptorRegistry.clientInterceptors.size}</div>`}else o+='<div style="color: #ff9800; font-size: 12px;">Registry not available</div>';if(o+="</div>",o+='<div style="margin-bottom: 12px; padding: 8px; background: rgba(100,200,0,0.1); border-radius: 4px;">',o+=`<div style="font-weight: bold; color: #81C784; margin-bottom: 4px;">🌐 Web Client Groups (${this.webClientGroups.size})</div>`,0===this.webClientGroups.size)o+='<div style="color: #666; font-size: 12px; font-style: italic;">No active web clients</div>';else for(const[n,r]of this.webClientGroups){if(o+='<div style="margin-bottom: 8px; padding: 6px; background: rgba(255,255,255,0.05); border-radius: 3px;">',o+=`<div style="font-weight: bold; font-size: 12px; color: #81C784;">Client: ${n.substring(0,8)}...</div>`,r.interceptorPipeline){const n=r.interceptorPipeline;if(o+='<div style="font-size: 11px; margin-top: 4px;">',o+='<span style="color: #4CAF50;">✓ Pipeline Active</span> | ',o+=`Interceptors: ${(null==(e=n.interceptors)?void 0:e.length)||0}`,o+="</div>",n.interceptors&&n.interceptors.length>0){o+='<div style="margin-top: 6px; padding-left: 8px; border-left: 2px solid #4CAF50;">';for(const e of n.interceptors){const n=e.getStats?e.getStats():{};o+='<div style="font-size: 10px; margin-bottom: 3px;">',o+=`<span style="color: #FFD54F;">🔧 ${e.name||"Unknown"}</span><br>`,o+=`<span style="color: #B0BEC5;">Enabled: ${(null==(t=n.config)?void 0:t.enabled)||!1}</span><br>`,"video-crop"===e.name?(o+=`<span style="color: #B0BEC5;">Cropping: ${n.enableCropping||"Unknown"}</span><br>`,n.cropRegion&&(o+=`<span style="color: #B0BEC5;">Region: ${n.cropRegion.x},${n.cropRegion.y} ${n.cropRegion.width}x${n.cropRegion.height}</span><br>`)):"brightness-filter"===e.name?o+=`<span style="color: #B0BEC5;">Brightness: ${n.brightness||"Unknown"}</span><br>`:"blur-effect"===e.name&&(o+=`<span style="color: #B0BEC5;">Blur Radius: ${n.blurRadius||"Unknown"}</span><br>`),o+=`<span style="color: #B0BEC5;">Frames: ${n.frameCount||0}</span>`,o+="</div>"}o+="</div>"}}else o+='<div style="font-size: 11px; margin-top: 4px; color: #ff9800;">⚠ No Pipeline</div>';o+="</div>"}o+="</div>",o+='<div style="padding: 8px; background: rgba(200,0,100,0.1); border-radius: 4px;">',o+='<div style="font-weight: bold; color: #F48FB1; margin-bottom: 4px;">📊 Performance</div>',o+='<div style="font-size: 11px; color: #B0BEC5;">',o+=`Active Connections: ${this.webClientGroups.size}<br>`,o+=`Target Tabs: ${this.tabGroups.size}<br>`,o+=`Last Update: ${(new Date).toLocaleTimeString()}`,o+="</div>",o+="</div>",n.innerHTML=o}catch(o){n.innerHTML=`<div style="color: #f44336; font-size: 12px;">Error updating debug panel: ${o.message}</div>`,console.error("[control-tab-manager] Error updating interceptor debug panel:",o)}}}window.controlTabManager||(console.log("[base-bundle] Injecting control tab manager..."),window.controlTabManager=new o);var r,i,a,s,c,l,d,g,h,p,b,u,m,f=e=>{throw TypeError(e)},w=(e,t,n)=>t.has(e)||f("Cannot "+n),C=(e,t,n)=>(w(e,t,"read from private field"),n?n.call(e):t.get(e)),y=(e,t,n)=>t.has(e)?f("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),v=(e,t,n,o)=>(w(e,t,"write to private field"),t.set(e,n),n),T=(e,t,n)=>(w(e,t,"access private method"),n);const I=void 0,k="ConnectionRefused",E="ConnectionError",P="GET",$={apiUrl:"http://localhost:9222",apiPath:"json/version",apiPathTargets:"json",apiPathNewTarget:"json/new",apiPathActivateTarget:"json/activate",apiPathCloseTarget:"json/close",connectionMaxRetry:20,connectionRetryDelay:500};class x{constructor(e){y(this,r),y(this,i,Object.assign({},S)),y(this,a,new Map),Object.assign(C(this,i),e);return["Runtime","Target","Page","Console","Network","Input","DOM","CSS","Debugger","Profiler","HeapProfiler","Security","ServiceWorker","Storage","SystemInfo","Browser","Emulation","Animation","Accessibility"].forEach(e=>{this[e]=this.createDomain(e)}),new Proxy(this,{get:(e,t)=>"string"==typeof t?(t in e||(e[t]=e.createDomain(t)),e[t]):e[t]})}createDomain(e){const t=this;return new Proxy(Object.create(null),{get(n,o){if("string"==typeof o)return"addEventListener"===o?t.getDomainListenerFunction("addEventListener",e):"removeEventListener"===o?t.getDomainListenerFunction("removeEventListener",e):(n[o]||(n[o]=t.getDomainMethodFunction(o,e)),n[o])}})}getDomainMethodFunction(e,t){const n=this;return async(o={},i)=>{await n.ready();const s=C(n,a).get(t);if(s!==I){for(;s.length>0;){const e=s.shift();e&&C(n,r)&&C(n,r)[e.methodName](`${e.domainName}.${e.type}`,e.listener)}C(n,a).delete(t)}if(!C(n,r))throw new Error("Connection not established");const c=o||{};return C(n,r).sendMessage(`${t}.${e}`,c,i)}}getDomainListenerFunction(e,t){const n=this;return(o,i)=>{if(C(n,r)===I){let r=C(n,a).get(t);r===I&&(r=[],C(n,a).set(t,r)),r.push({methodName:e,domainName:t,type:o,listener:i})}else C(n,r)[e](`${t}.${o}`,i)}}async ready(){if(C(this,r)===I){console.log("[kazeel:simple-cdp] CDP: Establishing connection...");let e=C(this,i).webSocketDebuggerUrl;if(e===I){console.log("[kazeel:simple-cdp] CDP: No WebSocket URL provided, fetching from API...");const t=new URL(C(this,i).apiPath,C(this,i).apiUrl);e=(await R(t,C(this,i))).webSocketDebuggerUrl,console.log(`[kazeel:simple-cdp] CDP: Retrieved WebSocket URL: ${e}`)}const t=new D(e);await t.open(),v(this,r,t),console.log("[kazeel:simple-cdp] CDP: Connection established successfully")}}get options(){return C(this,i)}set options(e){Object.assign(C(this,i),e)}get connection(){if(!C(this,r))throw new Error("Connection not established. Call a CDP method first to establish connection.");return C(this,r)}reset(){C(this,r)!==I?(console.log("[kazeel:simple-cdp] CDP: Resetting connection and clearing pending event listeners"),C(this,r).close(),v(this,r,I),C(this,a).clear(),console.log("[kazeel:simple-cdp] CDP: Reset completed")):console.log("[kazeel:simple-cdp] CDP: Reset called but no connection exists")}static getTargets(){const{apiPathTargets:e,apiUrl:t}=S;return R(new URL(e,t),S)}static createTarget(e){const{apiPathNewTarget:t,apiUrl:n}=S;return R(new URL(e?`${t}?${e}`:t,n),S,"PUT")}static async activateTarget(e){const{apiPathActivateTarget:t,apiUrl:n}=S;await R(new URL(`${t}/${e}`,n),S,P,!1)}static async closeTarget(e){const{apiPathCloseTarget:t,apiUrl:n}=S;await R(new URL(`${t}/${e}`,n),S,P,!1)}}r=new WeakMap,i=new WeakMap,a=new WeakMap;const S=Object.assign({},$);new x(S);class D extends EventTarget{constructor(e){super(),y(this,p),y(this,s),y(this,c),y(this,l,new Map),y(this,d,0),y(this,g),y(this,h,4e4),v(this,s,e)}open(){return console.log(`[kazeel:simple-cdp] CDP WebSocket: Attempting to connect to ${C(this,s)}`),v(this,c,new WebSocket(C(this,s))),C(this,c).addEventListener("message",e=>{T(this,p,m).call(this,JSON.parse(e.data))}),new Promise((e,t)=>{C(this,c).addEventListener("open",()=>{console.log("[kazeel:simple-cdp] CDP WebSocket: Connection opened successfully"),T(this,p,b).call(this),e()}),C(this,c).addEventListener("close",e=>{console.log(`[kazeel:simple-cdp] CDP WebSocket: Connection closed - Code: ${e.code}, Reason: ${e.reason}, WasClean: ${e.wasClean}`),T(this,p,u).call(this),t(new Error(e.reason))}),C(this,c).addEventListener("error",e=>{console.log("[kazeel:simple-cdp] CDP WebSocket: Error occurred",e),t(new Error("WebSocket error"))})})}sendMessage(e,t={},n){if(!C(this,c))throw new Error("WebSocket not connected");const o=C(this,d),r=JSON.stringify({id:o,method:e,params:t,sessionId:n});let i;v(this,d,(C(this,d)+1)%Number.MAX_SAFE_INTEGER),C(this,c).send(r);const a=new Promise((o,r)=>i={resolve:o,reject:r,method:e,params:t,sessionId:n});return C(this,l).set(o,i),a}close(){T(this,p,u).call(this),C(this,c)&&C(this,c).close()}}function R(e,t,n=P,o=!0){return M(async()=>{let t;try{t=await fetch(e,{method:n})}catch(r){const e=r;throw e.code=k,e}if(t.status>=400){const e=new Error(t.statusText||`HTTP Error ${t.status}`);throw e.status=t.status,e.code=E,e}return o?t.json():t.text()},t)}async function M(e,t,n=0){const{connectionMaxRetry:o,connectionRetryDelay:r}=t;try{return await e()}catch(i){if(i.code===k&&n<o)return await new Promise(e=>setTimeout(e,r)),M(e,t,n+1);throw i}}s=new WeakMap,c=new WeakMap,l=new WeakMap,d=new WeakMap,g=new WeakMap,h=new WeakMap,p=new WeakSet,b=function(){console.log(`[kazeel:simple-cdp] CDP WebSocket: Starting keep-alive with ${C(this,h)}ms interval`),v(this,g,setInterval(()=>{C(this,c)&&C(this,c).readyState===WebSocket.OPEN&&this.sendMessage("Browser.getVersion").catch(e=>{console.log("[kazeel:simple-cdp] CDP WebSocket: Keep-alive failed:",e)})},C(this,h)))},u=function(){C(this,g)&&(console.log("[kazeel:simple-cdp] CDP WebSocket: Stopping keep-alive"),clearInterval(C(this,g)),v(this,g,void 0))},m=function({id:e,method:t,result:n,error:o,params:r,sessionId:i}){if(e!==I){const t=C(this,l).get(e);if(t){const{resolve:r,reject:i}=t;if(o===I)r(n);else{const e=o.message+` when calling ${t.method}(${JSON.stringify(t.params)})${t.sessionId===I?"":` (sessionId ${JSON.stringify(t.sessionId)})`}`,n=new Error(e);n.code=o.code,i(n)}C(this,l).delete(e)}}if(t!==I){const e=new Event(t);e.params=r,e.sessionId=i,this.dispatchEvent(e)}};const W=Object.freeze(Object.defineProperty({__proto__:null,CDP:x,CONNECTION_ERROR_CODE:E,CONNECTION_REFUSED_ERROR_CODE:k,options:S},Symbol.toStringTag,{value:"Module"}))}();
