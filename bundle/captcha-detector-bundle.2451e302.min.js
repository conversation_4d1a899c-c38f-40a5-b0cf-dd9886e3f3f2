!function(){"use strict";var t=Object.defineProperty,e=(e,i,s)=>((e,i,s)=>i in e?t(e,i,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[i]=s)(e,"symbol"!=typeof i?i+"":i,s);class i{constructor(t,s={}){if(e(this,"name"),e(this,"type"),e(this,"isInitialized",!1),e(this,"isEnabled",!0),e(this,"config"),e(this,"stats"),e(this,"processor",null),e(this,"generator",null),e(this,"transformStream",null),e(this,"originalTrack",null),e(this,"processedTrack",null),e(this,"onConfigChange"),this.constructor===i)throw new Error("BaseInterceptor is an abstract class and cannot be instantiated directly");this.name=t,this.type=this.constructor.name,this.config={debug:!1,enabled:!0,...s},this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0},this.log(`${this.name} interceptor created`)}async initialize(t){if(!t||"video"!==t.kind)throw new Error(`${this.name} interceptor requires a valid video track`);this.originalTrack=t,this.log(`Initializing ${this.name} interceptor with video track:`,t.label);try{return this.processor=new MediaStreamTrackProcessor({track:t}),this.generator=new MediaStreamTrackGenerator({kind:"video"}),this.transformStream=new TransformStream({transform:this.processFrame.bind(this)}),this.processor.readable.pipeThrough(this.transformStream).pipeTo(this.generator.writable).catch(t=>{this.log(`Pipeline error in ${this.name}:`,t),this.stats.errorsEncountered++}),this.processedTrack=this.generator.track,this.isInitialized=!0,this.log(`${this.name} interceptor initialized successfully`),this.processedTrack}catch(e){throw this.log(`Error initializing ${this.name} interceptor:`,e),this.stats.errorsEncountered++,e}}async processFrame(t,e){const i=performance.now();try{if(this.stats.framesProcessed++,!this.isEnabled||!this.config.enabled)return void e.enqueue(t);const i=await this.processVideoFrame(t);e.enqueue(i),i!==t&&t.close()}catch(s){this.log(`Error processing frame in ${this.name}:`,s),this.stats.errorsEncountered++,e.enqueue(t)}finally{const t=performance.now()-i;this.stats.lastProcessingTime=t,this.stats.totalProcessingTime+=t,this.stats.averageProcessingTime=this.stats.totalProcessingTime/this.stats.framesProcessed}}updateConfig(t){if(!t||"object"!=typeof t)return void this.log("warn","Invalid configuration provided to updateConfig");const e={...this.config};this.config={...this.config,...t},this.log(`${this.name} configuration updated:`,{old:e,new:this.config}),this.onConfigChange&&this.onConfigChange(e,this.config)}getConfig(){return{...this.config}}enable(){this.isEnabled=!0,this.config.enabled=!0,this.log(`${this.name} interceptor enabled`)}disable(){this.isEnabled=!1,this.config.enabled=!1,this.log(`${this.name} interceptor disabled`)}getStats(){return{...this.stats}}resetStats(){this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0},this.log(`${this.name} stats reset`)}async cleanup(){try{this.processor&&(this.processor=null),this.generator&&(this.generator=null),this.transformStream&&(this.transformStream=null),this.originalTrack&&(this.originalTrack=null),this.processedTrack&&(this.processedTrack=null),this.isInitialized=!1,this.log(`${this.name} interceptor cleaned up`)}catch(t){this.log(`Error cleaning up ${this.name} interceptor:`,t)}}log(...t){this.config.debug&&console.log(`[${this.name}-Interceptor]`,...t)}getMetadata(){return{name:this.name,type:this.type,isInitialized:this.isInitialized,isEnabled:this.isEnabled,config:this.getConfig(),stats:this.getStats()}}}var s=Object.defineProperty,a=(t,e,i)=>((t,e,i)=>e in t?s(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i)(t,"symbol"!=typeof e?e+"":e,i);class n extends i{constructor(t="video-crop",e={}){super(t,{debug:!1,frameRate:30,enableCropping:!0,...e}),a(this,"subscribers",new Map),a(this,"frameCount",0),a(this,"lastFrameTime",0),this.log("VideoFrameInterceptor initialized",this.config)}async processVideoFrame(t){this.frameCount++;const e=performance.now();this.config.debug&&e-this.lastFrameTime>1e3&&(this.log(`Processed ${this.frameCount} frames`),this.lastFrameTime=e);let i=t;return this.config.enableCropping&&(this.config.cropRegion||this.config.defaultCropRegion)&&(i=this.applyCropping(t)),this.subscribers.size>0&&await this.notifySubscribers(i),i}applyCropping(t){var e;const{codedWidth:i,codedHeight:s}=t,a=this.config.cropRegion||this.config.defaultCropRegion;if(!a)return t;const n={x:this.makeEven(Math.max(0,Math.min(a.x,i))),y:this.makeEven(Math.max(0,Math.min(a.y,s))),width:this.makeEven(Math.max(2,Math.min(a.width,i-a.x))),height:this.makeEven(Math.max(2,Math.min(a.height,s-a.y)))};try{const i=new VideoFrame(t,{visibleRect:n,displayWidth:n.width,displayHeight:n.height,timestamp:t.timestamp,duration:null!=(e=t.duration)?e:void 0});return this.log("Frame cropped:",n),i}catch(r){return this.log("Cropping failed, using original frame:",r),t}}async notifySubscribers(t){var e;const i=[];for(const[a,n]of this.subscribers)try{const s=new VideoFrame(t,{timestamp:t.timestamp,duration:null!=(e=t.duration)?e:void 0}),r=n(s,{subscriberId:a,frameCount:this.frameCount,timestamp:performance.now(),cropRegion:this.config.cropRegion||this.config.defaultCropRegion||null});r instanceof Promise&&i.push(r)}catch(s){this.log(`Error notifying subscriber ${a}:`,s)}i.length>0&&await Promise.allSettled(i)}subscribe(t,e){if("function"!=typeof e)throw new Error("Callback must be a function");return this.subscribers.set(t,e),this.log(`Subscriber added: ${t} (total: ${this.subscribers.size})`),()=>this.unsubscribe(t)}unsubscribe(t){const e=this.subscribers.delete(t);return e&&this.log(`Subscriber removed: ${t} (remaining: ${this.subscribers.size})`),e}setCropRegion(t){t&&"object"==typeof t?(this.updateConfig({cropRegion:{x:this.makeEven(t.x||0),y:this.makeEven(t.y||0),width:this.makeEven(t.width||100),height:this.makeEven(t.height||100)}}),this.log("Crop region updated:",this.config.cropRegion)):(this.updateConfig({cropRegion:null}),this.log("Crop region cleared"))}setCroppingEnabled(t){this.updateConfig({enableCropping:!!t}),this.log("Cropping "+(this.config.enableCropping?"enabled":"disabled"))}getStatus(){return{...this.getMetadata(),enableCropping:this.config.enableCropping||!1,cropRegion:this.config.cropRegion||this.config.defaultCropRegion||null,subscriberCount:this.subscribers.size,frameCount:this.frameCount,hasOriginalTrack:!1,hasProcessedTrack:!1}}async cleanup(){this.log("Cleaning up video interceptor..."),this.subscribers.clear(),await super.cleanup(),this.log("Video interceptor cleanup complete")}makeEven(t){return 2*Math.floor(t/2)}}var r=Object.defineProperty,o=(t,e,i)=>((t,e,i)=>e in t?r(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i)(t,"symbol"!=typeof e?e+"":e,i);class h extends i{constructor(t="change-detector",e={}){super(t,{debug:!1,enabled:!1,changeThreshold:5,stabilityThreshold:1,consecutiveStableFrames:3,maxWaitDuration:5e3,pixelSampling:2,comparisonInterval:100,...e}),o(this,"canvas",null),o(this,"ctx",null),o(this,"isStreamPaused",!1),o(this,"isMonitoring",!1),o(this,"lastFrameData",null),o(this,"consecutiveStableCount",0),o(this,"maxWaitTimeout",null),o(this,"pendingFrames",[]),o(this,"lastStableFrame",null),o(this,"controlTabManager",null),o(this,"triggeringWebClientId",null),o(this,"pauseStartTime",null),this.log("ChangeDetectorInterceptor initialized",this.config)}setControlTabManager(t){this.controlTabManager=t,this.log("Control tab manager reference set for WebSocket messaging")}setTriggeringWebClient(t){this.triggeringWebClientId=t,this.log(`Triggering web client set: ${t}`)}sendWebSocketNotification(t,e={}){if(!this.controlTabManager||!this.triggeringWebClientId)return void this.log("Cannot send WebSocket notification - missing control tab manager or web client ID");const i={type:t,webClientId:this.triggeringWebClientId,timestamp:Date.now(),...e};this.log(`Sending WebSocket notification: ${t} to client ${this.triggeringWebClientId}`),this.controlTabManager.sendMessage(i)}async processVideoFrame(t){var e;try{if(!this.isMonitoring)return t;if(this.canvas&&this.canvas.width===t.codedWidth&&this.canvas.height===t.codedHeight||this.initializeCanvas(t.codedWidth,t.codedHeight),!this.ctx)throw new Error("Canvas context not available");this.ctx.drawImage(t,0,0);const i=this.ctx.getImageData(0,0,this.canvas.width,this.canvas.height).data;if(this.lastFrameData){const e=this.compareFrames(i,this.lastFrameData);if(this.config.debug&&this.log(`Frame comparison: ${e.toFixed(2)}% change`),e>(this.config.changeThreshold||5)&&!this.isStreamPaused){if(this.log(`📊 Significant change detected: ${e.toFixed(2)}% > ${this.config.changeThreshold}% - immediately pausing`),this.pauseStream(),this.lastStableFrame)return t.close(),this.lastStableFrame}else this.isStreamPaused&&e<=(this.config.stabilityThreshold||1)?this.handleStableFrame():this.isStreamPaused&&e>(this.config.stabilityThreshold||1)&&this.resetStability()}return this.lastFrameData=new Uint8ClampedArray(i),this.isStreamPaused||(this.lastStableFrame&&this.lastStableFrame.close(),this.lastStableFrame=new VideoFrame(t,{timestamp:t.timestamp,duration:null!=(e=t.duration)?e:void 0})),this.isStreamPaused&&this.lastStableFrame?this.lastStableFrame:t}catch(i){return this.log("Error in change detector:",i),t}}initializeCanvas(t,e){if(this.canvas=document.createElement("canvas"),this.canvas.width=t,this.canvas.height=e,this.ctx=this.canvas.getContext("2d"),!this.ctx)throw new Error("Failed to get 2D canvas context");this.log(`Canvas initialized: ${t}x${e}`)}compareFrames(t,e){let i=0;const s=t.length/4,a=this.config.pixelSampling||2;for(let n=0;n<t.length;n+=4*a){const s=Math.abs(t[n]-e[n]),a=Math.abs(t[n+1]-e[n+1]),r=Math.abs(t[n+2]-e[n+2]);(s>30||a>30||r>30)&&i++}return i/(s/a)*100}pauseStream(){this.isStreamPaused=!0,this.pauseStartTime=Date.now(),this.consecutiveStableCount=0,this.maxWaitTimeout&&clearTimeout(this.maxWaitTimeout),this.maxWaitTimeout=setTimeout(()=>{this.log("Maximum wait duration reached, resuming stream"),this.resumeStream()},this.config.maxWaitDuration||5e3),this.sendWebSocketNotification("stream-paused",{reason:"change-detected",pauseStartTime:this.pauseStartTime})}handleStableFrame(){this.consecutiveStableCount++,this.log(`Stable frame ${this.consecutiveStableCount}/${this.config.consecutiveStableFrames}`),this.consecutiveStableCount>=(this.config.consecutiveStableFrames||3)&&this.resumeStream()}resetStability(){this.consecutiveStableCount=0}resumeStream(){const t=this.pauseStartTime?Date.now()-this.pauseStartTime:0;this.isStreamPaused=!1,this.consecutiveStableCount=0,this.pauseStartTime=null,this.maxWaitTimeout&&(clearTimeout(this.maxWaitTimeout),this.maxWaitTimeout=null),this.log(`Stream resumed after ${t}ms`),this.sendWebSocketNotification("stream-resumed",{pauseDuration:t,resumeTime:Date.now()})}startMonitoring(){this.isMonitoring=!0,this.log("Change detection monitoring started")}stopMonitoring(){this.isMonitoring=!1,this.isStreamPaused=!1,this.consecutiveStableCount=0,this.maxWaitTimeout&&(clearTimeout(this.maxWaitTimeout),this.maxWaitTimeout=null),this.log("Change detection monitoring stopped")}async cleanup(){this.log("Cleaning up change detector interceptor..."),this.stopMonitoring(),this.canvas&&(this.canvas.width=0,this.canvas.height=0,this.canvas=null),this.ctx=null,this.lastFrameData=null,this.lastStableFrame&&(this.lastStableFrame.close(),this.lastStableFrame=null);for(const t of this.pendingFrames)t.close();this.pendingFrames=[],await super.cleanup(),this.log("Change detector interceptor cleanup complete")}}window.controlTabManager?(console.log("[captcha-detector-bundle] Registering video-crop interceptor..."),window.controlTabManager.interceptorRegistry.register("video-crop",n,{debug:!0,enabled:!0,enableCropping:!0,cropRegion:{x:0,y:0,width:window.innerWidth,height:window.innerHeight}}),console.log("[captcha-detector-bundle] Video-crop interceptor registered"),window.controlTabManager.interceptorRegistry.register("change-detector",h,{debug:!0,enabled:!1}),console.log("[captcha-detector-bundle] Change-detector interceptor registered")):console.error("[captcha-detector-bundle] controlTabManager does not exist")}();
