!function(){"use strict";var e=Object.defineProperty,t=(t,a,r)=>((t,a,r)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[a]=r)(t,"symbol"!=typeof a?a+"":a,r);class a{constructor(e,a,r){t(this,"signalingServerUrl"),t(this,"ws"),t(this,"tabId"),t(this,"isConnected"),t(this,"captureStream"),t(this,"controlTabPeerConnection"),this.signalingServerUrl=e,this.ws=null,this.tabId=a,this.isConnected=!1,this.captureStream=null,this.controlTabPeerConnection=null,console.log("[POC-Streamer] Target tab streamer initializing..."),this.init("true"===r)}async init(e){try{console.log("[POC-Streamer] Using tab ID:",this.tabId),await this.connectToSignalingServer(),e&&await this.handleStartStream(),console.log("[POC-Streamer] Target tab streamer initialized successfully")}catch(t){console.error("[POC-Streamer] Failed to initialize target streamer:",t)}}getTabId(){const e=new URLSearchParams(window.location.search).get("tabId")||window.name||document.title||window.location.hostname||this.generateId();return console.log("[POC-Streamer] Tab ID:",e),e}async connectToSignalingServer(){return new Promise((e,t)=>{this.ws=new WebSocket(this.signalingServerUrl),this.ws.onopen=()=>{console.log("[POC-Streamer] Connected to signaling server"),this.isConnected=!0,this.sendMessage({type:"register-target-tab",tabId:this.tabId,url:window.location.href,title:document.title,metadata:{userAgent:navigator.userAgent,timestamp:Date.now()}}),e()},this.ws.onmessage=e=>{try{const t=JSON.parse(e.data);this.handleMessage(t)}catch(t){console.error("[POC-Streamer] Failed to parse message:",t)}},this.ws.onclose=()=>{console.log("[POC-Streamer] Disconnected from signaling server"),this.isConnected=!1,setTimeout(()=>{this.isConnected||(console.log("[POC-Streamer] Attempting to reconnect..."),this.connectToSignalingServer().catch(console.error))},5e3)},this.ws.onerror=e=>{console.error("[POC-Streamer] WebSocket error:",e),t(e)},setTimeout(()=>{this.isConnected||t(new Error("Connection timeout"))},1e4)})}async handleMessage(e){switch(console.log("[POC-Streamer] Received message:",e.type),e.type){case"start-streaming":await this.handleStartStream();break;case"webrtc-answer-to-target":await this.handleWebRTCAnswer(e);break;case"webrtc-ice-candidate-to-target":await this.handleICECandidate(e);break;case"stop-streaming":break;default:console.log("[POC-Streamer] Unhandled message type:",e.type)}}async handleStartStream(){try{console.log("[POC-Streamer] Starting stream for:",this.tabId);const e=await this.captureCurrentTab();this.captureStream=e;const t=await this.createPeerConnection();e.getTracks().forEach(a=>{t.addTrack(a,e)});const a=await t.createOffer();await t.setLocalDescription(a),this.sendMessage({type:"webrtc-offer-from-target",offer:a,targetTabId:this.tabId}),this.sendMessage({type:"streaming-ready",tabId:this.tabId}),console.log("[POC-Streamer] Stream started and offer sent")}catch(e){console.error("[POC-Streamer] Failed to start stream:",e),this.sendMessage({type:"stream-error",tabId:this.tabId,error:e.message})}}async captureCurrentTab(){try{console.log("[POC-Streamer] Capturing current tab content...");const e=await navigator.mediaDevices.getDisplayMedia({video:{width:{ideal:1280},height:{ideal:720},frameRate:{ideal:100}},audio:!1,preferCurrentTab:!0});return console.log("[POC-Streamer] Tab content captured successfully"),console.log("[POC-Streamer] Stream active:",e.active),console.log("[POC-Streamer] Stream tracks:",e.getTracks()),e.getTracks().forEach((e,t)=>{console.log(`[POC-Streamer] Track ${t}:`,{kind:e.kind,enabled:e.enabled,muted:e.muted,readyState:e.readyState,label:e.label})}),e}catch(e){throw console.error("[POC-Streamer] Failed to capture tab content:",e),e}}async createPeerConnection(){const e=new RTCPeerConnection({iceServers:[{urls:"stun:stun.l.google.com:19302"}]});return this.controlTabPeerConnection=e,e.onicecandidate=e=>{e.candidate&&this.sendMessage({type:"webrtc-ice-candidate-from-target",candidate:e.candidate,targetTabId:this.tabId})},e.onconnectionstatechange=()=>{console.log("[POC-Streamer] Connection state:",e.connectionState)},e}async handleWebRTCAnswer(e){this.controlTabPeerConnection&&(await this.controlTabPeerConnection.setRemoteDescription(e.answer),console.log("[POC-Streamer] WebRTC answer processed"))}async handleICECandidate(e){this.controlTabPeerConnection&&await this.controlTabPeerConnection.addIceCandidate(e.candidate)}sendMessage(e){this.ws&&this.ws.readyState===WebSocket.OPEN&&this.ws.send(JSON.stringify(e))}generateId(){return Math.random().toString(36).substring(2,11)}cleanup(){console.log("[POC-Streamer] Cleaning up target streamer..."),this.captureStream&&this.captureStream.getTracks().forEach(e=>e.stop()),this.controlTabPeerConnection&&(this.controlTabPeerConnection.close(),this.controlTabPeerConnection=null),this.ws&&this.ws.close()}}if(!window.tabStreamer){console.log("[tab-streamer-bundle] Injecting tab streamer...");const e="${KAKU_WS_ENDPOINT}",t="${TAB_ID}",r="${AUTO_INITIALIZE}";window.tabStreamer=new a(e,t,r),window.addEventListener("beforeunload",()=>{window.tabStreamer&&window.tabStreamer.cleanup()})}}();
