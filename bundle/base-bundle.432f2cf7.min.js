!function(){"use strict";var e=Object.defineProperty,t=(t,n,r)=>((t,n,r)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[n]=r)(t,"symbol"!=typeof n?n+"":n,r);class n{constructor(e=[]){t(this,"interceptors"),t(this,"isInitialized",!1),t(this,"isEnabled",!0),t(this,"stats"),t(this,"processedTrack",null),t(this,"processor",null),t(this,"generator",null),t(this,"transformStream",null),t(this,"updateInterceptorConfigs",e=>{if(e&&"object"==typeof e){this.log("info","Updating interceptor configurations:",e);for(const n of this.interceptors){const r=e[n.name];if(r)try{n.updateConfig(r),this.log("info",`Updated config for interceptor: ${n.name}`)}catch(t){this.log("error",`Failed to update config for interceptor ${n.name}:`,t)}}}else this.log("warn","Invalid interceptor configurations provided")}),this.interceptors=[...e],this.isInitialized=!1,this.isEnabled=!0,this.stats={framesProcessed:0,errorsEncountered:0,averageProcessingTime:0,lastProcessingTime:0,totalProcessingTime:0,interceptorStats:new Map},this.log("InterceptorPipeline created with",this.interceptors.length,"interceptors")}async initialize(e){if(this.isInitialized)return this.log("Pipeline already initialized, returning existing track"),this.processedTrack;this.log("Initializing pipeline with video track");try{return this.processor=new MediaStreamTrackProcessor({track:e}),this.generator=new MediaStreamTrackGenerator({kind:"video"}),this.transformStream=new TransformStream({transform:this.processFrame.bind(this)}),this.processor.readable.pipeThrough(this.transformStream).pipeTo(this.generator.writable).catch(e=>{this.log("Pipeline error:",e),this.stats.errorsEncountered++}),this.processedTrack=this.generator,this.isInitialized=!0,this.log("Pipeline initialized successfully"),this.processedTrack}catch(t){throw this.log("Error initializing pipeline:",t),this.stats.errorsEncountered++,t}}async processFrame(e,t){const n=performance.now();try{if(this.stats.framesProcessed++,!this.isEnabled||0===this.interceptors.length)return void t.enqueue(e);if(null==e)return;let a=e;const i=[];for(let t=0;t<this.interceptors.length;t++){const n=this.interceptors[t];if(n&&n.config.enabled){console.log("Processing frame through interceptor:",n.name);try{const t=performance.now(),r=await n.processVideoFrame(a),o=performance.now()-t;this.updateInterceptorStats(n.name,o),r!==a&&a!==e&&i.push(a),a=r}catch(r){this.log(`Error in interceptor ${n.name}:`,r),this.stats.errorsEncountered++}}}if(t.enqueue(a),i.forEach(e=>{try{e.close()}catch(r){}}),a!==e)try{e.close()}catch(r){}const o=performance.now()-n;this.stats.lastProcessingTime=o,this.stats.totalProcessingTime+=o,this.stats.averageProcessingTime=this.stats.totalProcessingTime/this.stats.framesProcessed}catch(r){this.log("Error processing frame:",r),this.stats.errorsEncountered++,t.enqueue(e)}}updateInterceptorStats(e,t){let n=this.stats.interceptorStats.get(e);n||(n={framesProcessed:0,totalProcessingTime:0,averageProcessingTime:0},this.stats.interceptorStats.set(e,n)),n.framesProcessed++,n.totalProcessingTime+=t,n.averageProcessingTime=n.totalProcessingTime/n.framesProcessed}addInterceptor(e){this.interceptors.push(e),this.log(`Added interceptor ${e.name} at end of pipeline`)}removeInterceptor(e){const t=this.interceptors.findIndex(t=>t.name===e);if(-1!==t){const e=this.interceptors.splice(t,1)[0];return this.log(`Removed interceptor ${e.name} from pipeline`),!0}return this.log(`Interceptor ${e} not found in pipeline`),!1}getInterceptor(e){return this.interceptors.find(t=>t.name===e)||null}enable(){this.isEnabled=!0,this.log("Pipeline enabled")}disable(){this.isEnabled=!1,this.log("Pipeline disabled")}updateInterceptorConfig(e,t){const n=this.getInterceptor(e);if(!n)return this.log(`Interceptor ${e} not found`),!1;try{return n.updateConfig(t),this.log(`Updated config for interceptor: ${e}`),!0}catch(r){return this.log(`Failed to update config for interceptor ${e}:`,r),!1}}setEnabled(e){this.isEnabled=e,this.log("Pipeline "+(e?"enabled":"disabled"))}setInterceptorEnabled(e,t){const n=this.getInterceptor(e);n?(n.config.enabled=t,this.log(`Interceptor ${e} ${t?"enabled":"disabled"}`)):this.log(`Interceptor ${e} not found`)}getStats(){return{...this.stats}}resetStats(){this.stats.framesProcessed=0,this.stats.errorsEncountered=0,this.stats.averageProcessingTime=0,this.stats.lastProcessingTime=0,this.stats.totalProcessingTime=0,this.stats.interceptorStats.clear(),this.log("Pipeline statistics reset")}async cleanup(){if(this.log("Cleaning up pipeline resources"),this.transformStream){try{await this.transformStream.writable.close()}catch(e){}this.transformStream=null}this.processor&&(this.processor=null),this.generator&&(this.generator=null),this.processedTrack=null,this.isInitialized=!1,this.log("Pipeline cleanup completed")}log(...e){console.log("[InterceptorPipeline]",...e)}}var r=Object.defineProperty,a=(e,t,n)=>((e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n)(e,"symbol"!=typeof t?t+"":t,n);class i{constructor(){a(this,"interceptorClasses",new Map),a(this,"defaultConfigs",new Map),a(this,"clientInterceptors",new Map),a(this,"clientConfigs",new Map),this.log("InterceptorRegistry initialized")}register(e,t,n={}){if(!e||"string"!=typeof e)throw new Error("Interceptor name must be a non-empty string");if(!t||"function"!=typeof t)throw new Error("Interceptor class must be a constructor function");this.interceptorClasses.set(e,t),this.defaultConfigs.set(e,n),this.log(`Registered interceptor: ${e}`)}unregister(e){this.interceptorClasses.has(e)&&(this.interceptorClasses.delete(e),this.defaultConfigs.delete(e),this.log(`Unregistered interceptor: ${e}`))}getRegisteredInterceptors(){return Array.from(this.interceptorClasses.keys())}isRegistered(e){return this.interceptorClasses.has(e)}create(e,t){const n=this.interceptorClasses.get(e);if(!n)return this.log(`Warning: Interceptor class not found for ${e}`),null;const r={...this.defaultConfigs.get(e)||{},...t};try{return new n(e,r)}catch(a){return this.log(`Error creating interceptor ${e}:`,a),null}}setClientConfiguration(e,t=[],n={}){for(const a of t)if(!this.interceptorClasses.has(a))throw new Error(`Unknown interceptor: ${a}`);const r={interceptorNames:[...t],configs:new Map};for(const a of t){const e=this.defaultConfigs.get(a)||{},t=n[a]||{};r.configs.set(a,{...e,...t})}this.clientConfigs.set(e,r),this.log(`Set configuration for client ${e}:`,t)}getClientConfiguration(e){const t=this.clientConfigs.get(e);return t?{interceptorNames:[...t.interceptorNames],configs:new Map(t.configs)}:{interceptorNames:[],configs:new Map}}createClientInterceptors(e){if(this.clientInterceptors.has(e)){this.log(`Interceptors already exist for client ${e}, returning existing instances`);const t=this.clientInterceptors.get(e);return Array.from(t.values())}const t=this.getClientConfiguration(e),n=[],r=new Map;try{for(const a of t.interceptorNames){const i=this.interceptorClasses.get(a),o=t.configs.get(a)||{};if(!i){this.log(`Warning: Interceptor class not found for ${a}, skipping`);continue}const s=new i(a,o);n.push(s),r.set(a,s),this.log(`Created ${a} interceptor for client ${e}`)}return this.clientInterceptors.set(e,r),n}catch(a){for(const e of n)try{e.cleanup()}catch(i){this.log("Error cleaning up interceptor during creation failure:",i)}throw new Error(`Failed to create interceptors for client ${e}: ${a instanceof Error?a.message:String(a)}`)}}getClientInterceptors(e){const t=this.clientInterceptors.get(e);if(!t)return[];return this.getClientConfiguration(e).interceptorNames.map(e=>t.get(e)).filter(e=>void 0!==e)}updateClientInterceptorConfig(e,t,n){const r=this.clientConfigs.get(e);if(!r)throw new Error(`No configuration found for client: ${e}`);const a=r.configs.get(t)||{};r.configs.set(t,{...a,...n});const i=this.clientInterceptors.get(e);if(i&&i.has(t)){i.get(t).updateConfig(n)}this.log(`Updated ${t} config for client ${e}`)}cleanupClientInterceptors(e){const t=this.clientInterceptors.get(e);if(t){for(const[r,a]of t)try{a.cleanup(),this.log(`Cleaned up ${r} interceptor for client ${e}`)}catch(n){this.log(`Error cleaning up ${r} interceptor:`,n)}this.clientInterceptors.delete(e)}this.clientConfigs.delete(e),this.log(`Cleaned up all interceptors for client ${e}`)}getStats(){const e={registeredInterceptors:this.interceptorClasses.size,activeClients:this.clientInterceptors.size,totalActiveInterceptors:0,clientStats:{}};for(const[t,n]of this.clientInterceptors){const r=Array.from(n.values());e.totalActiveInterceptors+=r.length,e.clientStats[t]={interceptorCount:r.length,interceptors:r.map(e=>({name:e.name,type:e.type,isEnabled:e.isEnabled,stats:e.getStats()}))}}return e}log(...e){console.log("[InterceptorRegistry]",...e)}}var o=Object.defineProperty,s=(e,t,n)=>((e,t,n)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n)(e,"symbol"!=typeof t?t+"":t,n);class l{constructor(e={}){s(this,"options"),s(this,"connections"),s(this,"eventHandlers"),s(this,"mainCdpClient",null),this.options={wsEndpoint:e.wsEndpoint,debug:e.debug||!1,timeout:e.timeout||3e4,retryAttempts:e.retryAttempts||3,...e},this.connections=new Map,this.eventHandlers=new Map,this.log("[CDP-Manager] Initialized"),this.initializeMainCDPClient(this.options.wsEndpoint)}log(e,...t){this.options.debug&&console.log(`[CDP-Manager] ${e}`,...t)}async initializeMainCDPClient(e){if(!this.mainCdpClient)try{const{CDP:t}=await function(e){function t(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return Promise.resolve().then(n=>{for(const e of n||[])"rejected"===e.status&&t(e.reason);return e().catch(t)})}(async()=>{const{CDP:e}=await Promise.resolve().then(()=>Oe);return{CDP:e}});this.mainCdpClient=new t({webSocketDebuggerUrl:e}),await this.mainCdpClient.Target.setAutoAttach({autoAttach:!0,flatten:!0,waitForDebuggerOnStart:!1}),this.log("Main CDP client initialized successfully")}catch(t){throw this.log("Failed to initialize main CDP client:",t),t}}async addConnection(e){try{if(this.connections.has(e)){return this.connections.get(e)}await this.initializeMainCDPClient(this.options.wsEndpoint);const t=(await this.mainCdpClient.Target.attachToTarget({targetId:e,flatten:!0})).sessionId;await this.mainCdpClient.Runtime.enable(void 0,t);const n={client:this.mainCdpClient,sessionId:t,createdAt:Date.now(),isConnected:!0};return this.connections.set(e,n),this.log(`CDP connection established for tab: ${e} with session: ${t}`),n}catch(t){throw this.log(`Failed to add connection to tab ${e}:`,t),t}}async removeConnection(e){try{const n=this.connections.get(e);if(!n)return;if(this.mainCdpClient&&n.sessionId)try{await this.mainCdpClient.Target.detachFromTarget({sessionId:n.sessionId})}catch(t){this.log(`Failed to detach from target ${e}:`,t)}n.isConnected=!1,this.connections.delete(e),this.log(`CDP connection removed for tab: ${e}`)}catch(n){throw this.log(`Failed to remove connection from tab ${e}:`,n),n}}getConnection(e){return this.connections.get(e)||null}getAllConnections(){return new Map(this.connections)}async executeCommand(e,t,n={}){try{const r=this.connections.get(e);if(!r)throw new Error(`No connection found for tab: ${e}`);if(!r.isConnected)throw new Error(`Connection to tab ${e} is not active`);const[a,i]=t.split(".");if(!a||!i)throw new Error(`Invalid method format: ${t}. Expected format: Domain.method`);const o=r.client[a];if(!o)throw new Error(`Unsupported domain: ${a}`);const s=o[i];if("function"!=typeof s)throw new Error(`Unsupported method: ${i} in domain: ${a}`);return await s.call(o,n,r.sessionId)}catch(r){throw this.log(`Failed to execute ${t} on tab ${e}:`,r),r}}async getTargetTabInfo(e){try{const t=await this.executeCommand(e,"Runtime.evaluate",{expression:"({\n          width: window.innerWidth,\n          height: window.innerHeight,\n          url: window.location.href,\n          title: document.title\n        })",returnByValue:!0,awaitPromise:!0});return t.result.value}catch(t){return this.log(`Failed to get tab info for ${e}:`,t),{width:1920,height:1080,url:"unknown",title:"Unknown"}}}async executeScript(e,t){try{const n=await this.executeCommand(e,"Runtime.evaluate",{expression:t,returnByValue:!0});return n.result.value}catch(n){return this.log(`Failed to execute script on tab ${e}:`,n),null}}registerEventHandler(e,t){this.eventHandlers.set(e,t)}unregisterEventHandler(e){this.eventHandlers.delete(e)}async handleUserEvent(e,t){try{const n=this.connections.get(t);if(!n)throw new Error(`No CDP connection found for tab: ${t}`);if(!n.isConnected)throw new Error(`Connection to tab ${t} is not active`);const r=this.eventHandlers.get(e.eventType);if(!r)return void this.log(`No handler registered for event type: ${e.eventType}`);await r(e,t)}catch(n){throw this.log(`Failed to handle user event on tab ${t}:`,n),n}}async handleClickEvent(e,t){try{const n=await this.getTargetTabInfo(t);if(!n)throw new Error("Could not get target tab info");if("number"!=typeof e.x||"number"!=typeof e.y)throw new Error("Click event requires valid x and y coordinates");const r=e.x*n.width,a=e.y*n.height,i={type:"mousePressed",x:Math.round(r),y:Math.round(a),button:"left",clickCount:1,buttons:1};await this.executeCommand(t,"Input.dispatchMouseEvent",i),await new Promise(e=>setTimeout(e,50));const o={type:"mouseReleased",x:Math.round(r),y:Math.round(a),button:"left",clickCount:1,buttons:0};await this.executeCommand(t,"Input.dispatchMouseEvent",o)}catch(n){throw this.log(`Failed to handle click event on tab ${t}:`,n),n}}async handleScrollEvent(e,t){try{const n=await this.getTargetTabInfo(t);if(!n)throw new Error("Could not get target tab info");if("number"!=typeof e.x||"number"!=typeof e.y)throw new Error("Scroll event requires valid x and y coordinates");const r=e.x*n.width,a=e.y*n.height;await this.executeCommand(t,"Input.dispatchMouseEvent",{type:"mouseWheel",x:Math.round(r),y:Math.round(a),deltaX:e.deltaX||0,deltaY:e.deltaY||0})}catch(n){throw this.log(`Failed to handle scroll event on tab ${t}:`,n),n}}async handleKeyEvent(e,t){try{const n=this.mapBrowserEventTypeToCDP(e.eventType),r={type:n,key:e.key,code:e.code,keyCode:e.keyCode};"keyDown"===n&&e.text&&(r.text=e.text),await this.executeCommand(t,"Input.dispatchKeyEvent",r)}catch(n){throw this.log(`Failed to handle key event on tab ${t}:`,n),n}}async handleScrollGestureEvent(e,t){try{const n=await this.getTargetTabInfo(t);if(!n)throw new Error("Could not get target tab info");if("number"!=typeof e.x||"number"!=typeof e.y)throw new Error("Scroll gesture event requires valid x and y coordinates");if("number"!=typeof e.deltaY)throw new Error("Scroll gesture event requires valid deltaY");const r=e.x*n.width,a=e.y*n.height,i=-e.deltaY;await this.executeCommand(t,"Input.synthesizeScrollGesture",{x:Math.round(r),y:Math.round(a),yDistance:i,xDistance:0,speed:800,gestureSourceType:"touch"}),this.log(`Scroll gesture executed on tab ${t}: deltaY=${e.deltaY}, yDistance=${i}`)}catch(n){this.log(`Failed to handle scroll gesture on tab ${t}:`,n)}}mapBrowserEventTypeToCDP(e){return{keydown:"keyDown",keyup:"keyUp",keypress:"keyDown"}[e]||"keyDown"}initializeDefaultHandlers(){this.registerEventHandler("click",this.handleClickEvent.bind(this)),this.registerEventHandler("scroll",this.handleScrollEvent.bind(this)),this.registerEventHandler("scrollGesture",this.handleScrollGestureEvent.bind(this)),this.registerEventHandler("keydown",this.handleKeyEvent.bind(this)),this.registerEventHandler("keyup",this.handleKeyEvent.bind(this)),this.registerEventHandler("keypress",this.handleKeyEvent.bind(this))}async cleanup(){const e=Array.from(this.connections.keys()).map(e=>this.removeConnection(e));if(await Promise.all(e),this.mainCdpClient){try{this.mainCdpClient.connection.close()}catch(t){this.log("Error closing main CDP client:",t)}this.mainCdpClient=null}this.eventHandlers.clear(),this.log("CDP Manager cleanup completed")}getStats(){return{totalConnections:this.connections.size,eventHandlers:this.eventHandlers.size,connections:Array.from(this.connections.entries()).map(([e,t])=>({tabId:e,sessionId:t.sessionId,createdAt:t.createdAt}))}}}var c=Object.defineProperty,d=(e,t,n)=>((e,t,n)=>t in e?c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n)(e,"symbol"!=typeof t?t+"":t,n);class h{constructor(){d(this,"signalingServerUrl"),d(this,"wsBrowserEndpoint"),d(this,"websocket"),d(this,"isConnected"),d(this,"rtcConfig"),d(this,"interceptorRegistry"),d(this,"webClientGroups"),d(this,"tabGroups"),d(this,"targetConnections"),d(this,"cdpManager"),d(this,"clientId"),this.signalingServerUrl="${KAKU_WS_ENDPOINT}",this.wsBrowserEndpoint="${BROWSER_WS_ENDPOINT}",this.websocket=null,this.isConnected=!1,this.rtcConfig={iceServers:[{urls:"stun:stun.cloudflare.com:3478"},{urls:"stun:stun.l.google.com:19302"}],iceCandidatePoolSize:10},this.webClientGroups=new Map,this.tabGroups=new Map,this.targetConnections=new Map,this.interceptorRegistry=new i,this.cdpManager=null,this.init()}async init(){console.log("[control-tab-manager] Initializing control tab manager..."),this.initializeCDPManager(),await this.connectToSignalingServer(),this.setupPageListeners(),this.createControlTabUI()}initializeCDPManager(){var e;try{console.log("[control-tab-manager] Checking for CDPManager availability..."),console.log("[control-tab-manager] CDPManager found, initializing..."),this.cdpManager=new l({wsEndpoint:this.wsBrowserEndpoint,debug:!0}),null==(e=this.cdpManager)||e.initializeDefaultHandlers(),console.log("[control-tab-manager] CDP Manager initialized successfully"),console.log("[control-tab-manager] CDP Manager instance:",this.cdpManager)}catch(t){throw console.error("[control-tab-manager] Failed to initialize CDP Manager:",t),t}}initializePipeline(e){const t=this.webClientGroups.get(e);if(!t)throw console.warn("[control-tab-manager] No web client group found for:",e),new Error("No web client group found for "+e);try{const r=this.interceptorRegistry.createClientInterceptors(e),a=new n(r);return t.interceptorPipeline=a,setTimeout(()=>this.updateInterceptorDebugPanel(),100),a}catch(r){throw console.error("[control-tab-manager] Error initializing interceptors for client:",e,r),r}}getClientInterceptorConfig(e){return this.interceptorRegistry.getClientConfiguration(e)}processStreamForClient(e,t){const n=this.webClientGroups.get(t),r=this.getClientInterceptorConfig(t);if(!n)throw console.warn("Cannot process stream - No webClientGroup found for:",t),new Error("No webClientGroup found for "+t);if(!r)return e;if(0===r.interceptorNames.length)return e;try{const a=e.getVideoTracks()[0];if(!a)return e;let i=n.interceptorPipeline;if(!i&&(i=this.initializePipeline(t),!i))return console.warn("[control-tab-manager] Failed to initialize interceptors for client:",t),e;console.log("[control-tab-manager] Processing stream through interceptor pipeline for client:",t,"interceptors:",r.interceptorNames);const o=i.initialize(a),s=[o,...e.getAudioTracks()].filter(e=>e instanceof MediaStreamTrack);return new MediaStream(s)}catch(a){return console.error("[control-tab-manager] Error processing stream through interceptor pipeline for client:",t,a),e}}async connectToSignalingServer(){try{console.log("[control-tab-manager] Connecting to signaling server..."),this.websocket=new WebSocket(this.signalingServerUrl),this.websocket.onopen=()=>{console.log("[control-tab-manager] Control tab connected to signaling server"),this.isConnected=!0,this.sendMessage({type:"register-control-tab",metadata:{userAgent:navigator.userAgent,timestamp:Date.now()}})},this.websocket.onmessage=e=>{try{const t=JSON.parse(e.data);this.handleMessage(t)}catch(t){console.error("[control-tab-manager] Failed to parse message:",t)}},this.websocket.onclose=()=>{console.log("[control-tab-manager] Control tab disconnected from signaling server"),this.isConnected=!1,this.scheduleReconnect()},this.websocket.onerror=e=>{console.error("[control-tab-manager] Control tab WebSocket error:",e)}}catch(e){console.error("[control-tab-manager] Failed to connect to signaling server:",e),this.scheduleReconnect()}}scheduleReconnect(){console.log("[control-tab-manager] Scheduling reconnection in 5 seconds..."),setTimeout(()=>{this.connectToSignalingServer()},5e3)}sendMessage(e){this.websocket&&this.websocket.readyState===WebSocket.OPEN?this.websocket.send(JSON.stringify(e)):console.warn("[control-tab-manager] Cannot send message - not connected")}handleMessage(e){switch(console.log("[control-tab-manager] Control tab received message:",e.type),e.type){case"target-tabs-list":this.handleTargetTabsList(e);break;case"target-tab-registered":this.handleTargetTabRegistered(e);break;case"target-tab-disconnected":this.handleTargetTabDisconnected(e);break;case"stream-stopped":this.handleStreamStopped(e);break;case"webrtc-offer":this.handleWebRTCOffer();break;case"webrtc-answer":this.handleWebRTCAnswer(e);break;case"webrtc-ice-candidate":this.handleWebRTCIceCandidate(e);break;case"webrtc-offer-from-target":this.handleTargetTabOffer(e);break;case"webrtc-ice-candidate-from-target":this.handleTargetTabIceCandidate(e);break;case"webrtc-answer-from-web-client":this.handleWebClientAnswer(e);break;case"webrtc-ice-candidate-from-web-client":this.handleWebClientIceCandidate(e);break;case"web-client-registered":this.handleWebClientRegistered(e);break;case"web-client-disconnected":this.handleWebClientDisconnected(e);break;default:console.log("[control-tab-manager] Unknown message type:",e.type)}}handleTargetTabsList(e){console.log("[control-tab-manager] Received target tabs list:",e.targetTabs.length),this.tabGroups.clear(),e.targetTabs.forEach(e=>{this.tabGroups.set(e.tabId,{tabInfo:e,stream:null})})}handleTargetTabRegistered(e){console.log("[control-tab-manager] Target tab registered:",e.tabId),this.tabGroups.set(e.tabId,{tabInfo:e,stream:null})}handleTargetTabDisconnected(e){console.log("[control-tab-manager] Target tab disconnected:",e.tabId);const t=this.tabGroups.get(e.tabId);(null==t?void 0:t.stream)&&this.cleanupStream(e.tabId),this.tabGroups.delete(e.tabId)}handleWebClientRegistered(e){console.log("[control-tab-manager] Web client registered:",e.webClientId),this.webClientGroups.set(e.webClientId,{RTCPeerConnection:null,interceptorPipeline:new n,dataChannel:null,webClient:{clientInfo:e.metadata||{},currentTabId:null,connected:!0}}),this.createPeerConnectionForWebClient(e.webClientId)}handleWebClientDisconnected(e){console.log("[control-tab-manager] Web client disconnected:",e.webClientId),this.cleanupWebClient(e.webClientId)}handleStreamStopped(e){console.log("[control-tab-manager] Stream stopped for tab:",e.targetTabId),this.cleanupStream(e.targetTabId)}async handleWebRTCOffer(){console.log("[control-tab-manager] Received WebRTC offer")}async handleWebRTCAnswer(e){console.log("[control-tab-manager] Received WebRTC answer for web client:",e.webClientId);const t=this.webClientGroups.get(e.webClientId),n=null==t?void 0:t.RTCPeerConnection;if(n)try{await n.setRemoteDescription(new RTCSessionDescription(e.answer)),console.log("[control-tab-manager] WebRTC connection established for web client:",e.webClientId)}catch(r){console.error("[control-tab-manager] Failed to set remote description:",r)}}async handleWebRTCIceCandidate(e){const t=this.webClientGroups.get(e.webClientId),n=null==t?void 0:t.RTCPeerConnection;if(n)try{await n.addIceCandidate(e.candidate)}catch(r){console.error("[control-tab-manager] Failed to add ICE candidate:",r)}}async handleTargetTabOffer(e){console.log("[control-tab-manager] Received WebRTC offer from target tab:",e);const t={targetTabId:e.targetTabId,status:"connecting"},n=new RTCPeerConnection(this.rtcConfig);this.targetConnections.set(e.targetTabId,n),n.ontrack=async n=>{console.log("[control-tab-manager] Target stream event:",n);const[r]=n.streams,a=this.tabGroups.get(e.targetTabId);a&&(a.stream=r),console.log(`[control-tab-manager] Stored active stream for tab ${e.targetTabId}`);try{console.log(`[control-tab-manager] Attempting to establish CDP connection for tab: ${e.targetTabId}`),console.log("[control-tab-manager] CDP Manager available:",!!this.cdpManager),this.cdpManager?(console.log(`[control-tab-manager] Adding CDP connection for tab: ${e.targetTabId}`),console.log(`[control-tab-manager] Creating CDP target info for tab: ${e.targetTabId}`),await this.cdpManager.addConnection(e.targetTabId),console.log(`[control-tab-manager] CDP connection established for tab: ${e.targetTabId}`)):console.warn("[control-tab-manager] CDP Manager not available - user events will not work")}catch(i){console.error(`[control-tab-manager] Failed to establish CDP connection for tab ${e.targetTabId}:`,i)}this.displayStreamInControlTab(e.targetTabId,r,t),this.broadcastStreamToAllClients(r,e.targetTabId)},n.onicecandidate=e=>{e.candidate&&this.sendMessage({type:"webrtc-ice-candidate-to-target",candidate:e.candidate,targetTabId:t.targetTabId})};try{await n.setRemoteDescription(new RTCSessionDescription(e.offer));const r=await n.createAnswer();await n.setLocalDescription(r),this.sendMessage({type:"webrtc-answer-to-target",answer:r,targetTabId:t.targetTabId}),console.log("[control-tab-manager] WebRTC answer sent to target tab")}catch(r){console.error("[control-tab-manager] Failed to handle target tab offer:",r)}}async handleTargetTabIceCandidate(e){const t=this.targetConnections.get(e.targetTabId);if(t)try{await t.addIceCandidate(e.candidate)}catch(n){console.error("[control-tab-manager] Failed to add target ICE candidate:",n)}}async handleWebClientAnswer(e){console.log("[control-tab-manager] Received WebRTC answer from web client:",e.webClientId);const t=this.webClientGroups.get(e.webClientId),n=null==t?void 0:t.RTCPeerConnection;if(n)try{await n.setRemoteDescription(new RTCSessionDescription(e.answer)),console.log("[control-tab-manager] WebRTC connection established with web client:",e.webClientId)}catch(r){console.error("[control-tab-manager] Failed to set remote description from web client:",r)}else console.warn("[control-tab-manager] No peer connection found for web client:",e.webClientId)}async handleWebClientIceCandidate(e){console.log("[control-tab-manager] Received ICE candidate from web client:",e.webClientId);const t=this.webClientGroups.get(e.webClientId),n=null==t?void 0:t.RTCPeerConnection;if(n)try{await n.addIceCandidate(e.candidate)}catch(r){console.error("[control-tab-manager] Failed to add ICE candidate from web client:",r)}else console.warn("[control-tab-manager] No peer connection found for web client:",e.webClientId)}createPeerConnectionForWebClient(e){console.log("[control-tab-manager] Creating peer connection for web client:",e);const t=new RTCPeerConnection(this.rtcConfig),n=t.createDataChannel("userEvents",{ordered:!0});this.setupDataChannelHandlers(n,e),t.onicecandidate=t=>{t.candidate&&this.sendMessage({type:"webrtc-ice-candidate-to-web-client",candidate:t.candidate,webClientId:e})};const r=this.webClientGroups.get(e);r&&(r.RTCPeerConnection=t,r.dataChannel=n),this.addExistingTracksToNewClient(e,t),console.log("[control-tab-manager] Peer connection created for web client:",e)}addExistingTracksToNewClient(e,t){console.log("[control-tab-manager] Checking for existing active streams for new client:",e);for(const[n,r]of this.tabGroups)if(r.stream){console.log(`[control-tab-manager] Found active stream for tab ${n}, adding tracks to new client ${e}`);const a=r.stream;if(a&&a.getTracks().length>0){const r=this.processStreamForClient(a,e),i=this.webClientGroups.get(e);(null==i?void 0:i.webClient)&&(i.webClient.currentTabId=n),r.getTracks().forEach(n=>{console.log(`[control-tab-manager] Adding existing ${n.kind} track to new client:`,e),t.addTrack(n,r)}),this.createOfferToWebClient(n,e),console.log(`[control-tab-manager] Added existing stream tracks from tab ${n} to new client ${e}`);break}}Array.from(this.tabGroups.values()).some(e=>e.stream)||console.log("[control-tab-manager] No existing active streams found for new client:",e)}cleanupWebClient(e){console.log("[control-tab-manager] Cleaning up web client:",e);const t=this.webClientGroups.get(e);t&&(t.RTCPeerConnection&&t.RTCPeerConnection.close(),t.dataChannel&&t.dataChannel.close()),this.webClientGroups.delete(e),console.log("[control-tab-manager] Web client cleanup completed:",e)}broadcastStreamToAllClients(e,t){console.log("[control-tab-manager] Broadcasting stream to all connected web clients for tab:",t);for(const[n,r]of this.webClientGroups){const a=r.RTCPeerConnection;if(!a)continue;console.log("[control-tab-manager] Processing web client:",n,"- Connection state:",a.connectionState),r.webClient&&(r.webClient.currentTabId=t);const i=this.processStreamForClient(e,n);i.getTracks().forEach(e=>{const t=a.getSenders().find(t=>t.track&&t.track.kind===e.kind);t?(console.log(`[control-tab-manager] Replacing ${e.kind} track for client:`,n),t.replaceTrack(e)):(console.log(`[control-tab-manager] Adding ${e.kind} track for client:`,n),a.addTrack(e,i))});const o=a.connectionState,s=a.signalingState;"connected"===o?console.log(`[control-tab-manager] Client ${n} already connected - tracks updated via existing connection`):"stable"!==s||"new"!==o&&"connecting"!==o?"stable"!==s?console.log(`[control-tab-manager] Skipping offer for client ${n} - negotiation in progress (signaling: ${s})`):(console.log(`[control-tab-manager] Creating offer for client ${n} (connection: ${o}, signaling: ${s})`),this.createOfferToWebClient(t,n)):(console.log(`[control-tab-manager] Creating new offer for client ${n} (connection: ${o}, signaling: ${s})`),this.createOfferToWebClient(t,n))}console.log("[control-tab-manager] Stream broadcast completed to",this.webClientGroups.size,"clients")}async createOfferToWebClient(e,t){const n=this.webClientGroups.get(t),r=null==n?void 0:n.RTCPeerConnection;if(!r)return void console.error("[control-tab-manager] No peer connection found for web client:",t);const a=r.connectionState,i=r.signalingState;console.log(`[control-tab-manager] Creating offer for client ${t} - Connection: ${a}, Signaling: ${i}`);try{const n=await r.createOffer();await r.setLocalDescription(n),this.sendMessage({type:"webrtc-offer-to-web-client",offer:n,targetClientId:t,tabId:e,fromClientId:this.clientId}),console.log(`[control-tab-manager] WebRTC offer sent to web client: ${t} (${a} -> negotiating)`)}catch(o){console.error(`[control-tab-manager] Failed to create offer to web client ${t}:`,o.message),console.error(`[control-tab-manager] Error context - Connection: ${a}, Signaling: ${i}`)}}cleanupStream(e){var t;console.log("[control-tab-manager] Cleaning up stream for tab:",e);const n=this.targetConnections.get(e);n&&(n.close(),this.targetConnections.delete(e));for(const[s,l]of this.webClientGroups)if((null==(t=l.webClient)?void 0:t.currentTabId)===e){l.webClient.currentTabId=null;const e=l.RTCPeerConnection;e&&e.getSenders().forEach(t=>{t.track&&e.removeTrack(t)})}const r=document.getElementById(`poc-stream-${e}`);r&&r.remove();const a=document.getElementById("poc-streams-container");a&&0===a.children.length&&(a.innerHTML='<div style="color: #666; font-style: italic;">No active streams</div>');const i=this.tabGroups.get(e);i&&(i.stream=null);try{this.cdpManager&&(this.cdpManager.removeConnection(e),console.log(`[control-tab-manager] CDP connection cleaned up for tab: ${e}`))}catch(o){console.error(`[control-tab-manager] Failed to cleanup CDP connection for tab ${e}:`,o)}}createControlTabUI(){const e=document.createElement("div");e.id="poc-control-panel",e.style.cssText="\n        position: fixed;\n        top: 20px;\n        right: 20px;\n        width: 400px;\n        max-height: 600px;\n        background: rgba(0, 0, 0, 0.9);\n        color: white;\n        border-radius: 8px;\n        padding: 16px;\n        z-index: 10000;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        font-size: 14px;\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n        overflow-y: auto;\n      ",e.innerHTML='\n        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">\n          <h3 style="margin: 0; color: #4CAF50;">POC Control Tab</h3>\n          <button id="poc-toggle-panel" style="background: none; border: 1px solid #666; color: white; padding: 4px 8px; border-radius: 4px; cursor: pointer;">−</button>\n        </div>\n        <div id="poc-panel-content">\n          <div style="margin-bottom: 12px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">Status:</div>\n            <div id="poc-connection-status" style="color: #ff9800;">Connecting...</div>\n          </div>\n          <div style="margin-bottom: 12px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">Active Streams:</div>\n            <div id="poc-streams-container" style="max-height: 300px; overflow-y: auto;">\n              <div style="color: #666; font-style: italic;">No active streams</div>\n            </div>\n          </div>\n          <div style="margin-bottom: 12px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">Target Tabs:</div>\n            <div id="poc-tabs-container" style="max-height: 200px; overflow-y: auto;">\n              <div style="color: #666; font-style: italic;">No target tabs</div>\n            </div>\n          </div>\n          <div style="margin-bottom: 12px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">Interceptor Debug Panel:</div>\n            <div id="poc-interceptor-debug-container" style="max-height: 400px; overflow-y: auto; border: 1px solid #444; border-radius: 4px; padding: 8px; background: rgba(255,255,255,0.05);">\n              <div style="color: #666; font-style: italic;">No interceptor data</div>\n            </div>\n            <button id="poc-refresh-interceptors" style="margin-top: 8px; background: #4CAF50; border: none; color: white; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">Refresh Interceptor Status</button>\n          </div>\n        </div>\n      ',document.body.appendChild(e);const t=document.getElementById("poc-toggle-panel"),n=document.getElementById("poc-panel-content");let r=!1;null==t||t.addEventListener("click",()=>{r=!r,n&&(n.style.display=r?"none":"block"),t&&(t.textContent=r?"+":"−"),e.style.height=r?"auto":""});const a=document.getElementById("poc-refresh-interceptors");a&&a.addEventListener("click",()=>{this.updateInterceptorDebugPanel()}),this.updateConnectionStatus("Connected"),this.updateInterceptorDebugPanel()}updateConnectionStatus(e){const t=document.getElementById("poc-connection-status");t&&(t.textContent=e,t.style.color="Connected"===e?"#4CAF50":"#ff9800")}displayStreamInControlTab(e,t,n){var r;console.log("[control-tab-manager] Displaying stream in control tab for tab:",e);const a=document.getElementById("poc-streams-container");if(!a)return void console.warn("[control-tab-manager] Streams container not found");const i=a.querySelector('[style*="font-style: italic"]');i&&(null==(r=i.textContent)?void 0:r.includes("No active streams"))&&i.remove();const o=document.createElement("div");o.id=`poc-stream-${e}`,o.style.cssText="\n        margin-bottom: 12px;\n        padding: 8px;\n        border: 1px solid #333;\n        border-radius: 4px;\n        background: rgba(255, 255, 255, 0.05);\n      ",o.innerHTML=`\n        <div style="font-weight: bold; margin-bottom: 8px; color: #4CAF50;">\n          📺 Tab ${e.substring(0,8)}...\n        </div>\n        <video\n          id="poc-video-${e}"\n          autoplay\n          muted\n          playsinline\n          style="width: 100%; height: 150px; background: #000; border-radius: 4px; object-fit: contain;"\n        ></video>\n        <div style="margin-top: 8px; font-size: 12px; color: #ccc;">\n          Target: ${n.targetTabId||"Unknown"}\n        </div>\n      `,a.appendChild(o);const s=document.getElementById(`poc-video-${e}`);s&&t&&(s.srcObject=t,s.onloadedmetadata=()=>{console.log("[control-tab-manager] Video metadata loaded in control tab")},s.onplay=()=>{console.log("[control-tab-manager] Video started playing in control tab")},s.onerror=e=>{console.error("[control-tab-manager] Video error in control tab:",e)})}setupPageListeners(){window.addEventListener("beforeunload",()=>{console.log("[control-tab-manager] Control tab unloading...");for(const[e,t]of this.tabGroups)t.stream&&this.cleanupStream(e);this.websocket&&this.websocket.close()})}setupDataChannelHandlers(e,t){console.log("[control-tab-manager] Setting up data channel for web client:",t),e.onopen=()=>{console.log("[control-tab-manager] Data channel opened for web client:",t)},e.onclose=()=>{console.log("[control-tab-manager] Data channel closed for web client:",t)},e.onerror=e=>{console.error("[control-tab-manager] Data channel error:",e)},e.onmessage=e=>{var n;try{const r=JSON.parse(e.data);console.log("[control-tab-manager] Received user event:",r);const a=this.webClientGroups.get(t),i=null==(n=null==a?void 0:a.webClient)?void 0:n.currentTabId;console.log("[control-tab-manager] Web client group:",a),console.log("[control-tab-manager] Target tab ID:",i),console.log("[control-tab-manager] CDP Manager available:",!!this.cdpManager),i?(console.log("[control-tab-manager] Handling user event for target tab:",i),this.handleUserEvent(t,r,i)):console.warn("[control-tab-manager] No target tab for user event from client:",t)}catch(r){console.error("[control-tab-manager] Failed to parse user event:",r)}}}async handleUserEvent(e,t,n){if("user-event"===t.type){"click"===t.eventType&&this.enableChangeDetectorForClient(e);try{await this.replayEventOnTargetTab(t,n)}catch(r){console.error("[control-tab-manager] Failed to replay event:",r)}}else console.warn("[control-tab-manager] Unknown event type:",t.type)}enableChangeDetectorForClient(e){console.log("[control-tab-manager] Enabling Change detector for client:",e,"due to click event");const t=this.webClientGroups.get(e);if(!t)return void console.warn("[control-tab-manager] No web client group found for:",e);const n=t.interceptorPipeline;if(!n)return void console.warn("[control-tab-manager] No interceptor pipeline found for client:",e);const r=n.getInterceptor("change-detector");r?(r.setControlTabManager(this),r.setTriggeringWebClient(e),r.updateConfig({enabled:!0}),console.log(`[control-tab-manager] Change detector interceptor enabled for client ${e}`)):console.warn(`[control-tab-manager] Change detector interceptor not found for client ${e}`)}async replayEventOnTargetTab(e,t){if(this.cdpManager)try{await this.cdpManager.handleUserEvent(e,t)}catch(n){throw console.error("[control-tab-manager] Failed to replay event via CDPManager:",n),n}else console.warn("[control-tab-manager] CDP Manager not available - cannot replay event")}toggleInterceptor(e){console.log("[control-tab-manager] Interceptor controls are now per-client. Use the web client interface.")}toggleCropping(e){console.log("[control-tab-manager] Cropping controls are now per-client. Use the web client interface.")}setCropRegion(e){console.log("[control-tab-manager] Crop region controls are now per-client. Use the web client interface.")}updateInterceptorDebugPanel(){var e,t;const n=document.getElementById("poc-interceptor-debug-container");if(n)try{let r="";if(r+='<div style="margin-bottom: 12px; padding: 8px; background: rgba(0,100,200,0.1); border-radius: 4px;">',r+='<div style="font-weight: bold; color: #64B5F6; margin-bottom: 4px;">📋 Interceptor Registry</div>',void 0!==this.interceptorRegistry){r+=`<div style="font-size: 12px;">Registered: ${Array.from(this.interceptorRegistry.interceptorClasses.keys()).join(", ")||"None"}</div>`,r+=`<div style="font-size: 12px;">Total Clients: ${this.interceptorRegistry.clientInterceptors.size}</div>`}else r+='<div style="color: #ff9800; font-size: 12px;">Registry not available</div>';if(r+="</div>",r+='<div style="margin-bottom: 12px; padding: 8px; background: rgba(100,200,0,0.1); border-radius: 4px;">',r+=`<div style="font-weight: bold; color: #81C784; margin-bottom: 4px;">🌐 Web Client Groups (${this.webClientGroups.size})</div>`,0===this.webClientGroups.size)r+='<div style="color: #666; font-size: 12px; font-style: italic;">No active web clients</div>';else for(const[n,a]of this.webClientGroups){if(r+='<div style="margin-bottom: 8px; padding: 6px; background: rgba(255,255,255,0.05); border-radius: 3px;">',r+=`<div style="font-weight: bold; font-size: 12px; color: #81C784;">Client: ${n.substring(0,8)}...</div>`,a.interceptorPipeline){const n=a.interceptorPipeline;if(r+='<div style="font-size: 11px; margin-top: 4px;">',r+='<span style="color: #4CAF50;">✓ Pipeline Active</span> | ',r+=`Interceptors: ${(null==(e=n.interceptors)?void 0:e.length)||0}`,r+="</div>",n.interceptors&&n.interceptors.length>0){r+='<div style="margin-top: 6px; padding-left: 8px; border-left: 2px solid #4CAF50;">';for(const e of n.interceptors){const n=e.getStats?e.getStats():{};r+='<div style="font-size: 10px; margin-bottom: 3px;">',r+=`<span style="color: #FFD54F;">🔧 ${e.name||"Unknown"}</span><br>`,r+=`<span style="color: #B0BEC5;">Enabled: ${(null==(t=n.config)?void 0:t.enabled)||!1}</span><br>`,"video-crop"===e.name?(r+=`<span style="color: #B0BEC5;">Cropping: ${n.enableCropping||"Unknown"}</span><br>`,n.cropRegion&&(r+=`<span style="color: #B0BEC5;">Region: ${n.cropRegion.x},${n.cropRegion.y} ${n.cropRegion.width}x${n.cropRegion.height}</span><br>`)):"brightness-filter"===e.name?r+=`<span style="color: #B0BEC5;">Brightness: ${n.brightness||"Unknown"}</span><br>`:"blur-effect"===e.name&&(r+=`<span style="color: #B0BEC5;">Blur Radius: ${n.blurRadius||"Unknown"}</span><br>`),r+=`<span style="color: #B0BEC5;">Frames: ${n.frameCount||0}</span>`,r+="</div>"}r+="</div>"}}else r+='<div style="font-size: 11px; margin-top: 4px; color: #ff9800;">⚠ No Pipeline</div>';r+="</div>"}r+="</div>",r+='<div style="padding: 8px; background: rgba(200,0,100,0.1); border-radius: 4px;">',r+='<div style="font-weight: bold; color: #F48FB1; margin-bottom: 4px;">📊 Performance</div>',r+='<div style="font-size: 11px; color: #B0BEC5;">',r+=`Active Connections: ${this.webClientGroups.size}<br>`,r+=`Target Tabs: ${this.tabGroups.size}<br>`,r+=`Last Update: ${(new Date).toLocaleTimeString()}`,r+="</div>",r+="</div>",n.innerHTML=r}catch(r){n.innerHTML=`<div style="color: #f44336; font-size: 12px;">Error updating debug panel: ${r.message}</div>`,console.error("[control-tab-manager] Error updating interceptor debug panel:",r)}}}window.controlTabManager||(console.log("[base-bundle] Injecting control tab manager..."),window.controlTabManager=new h);var f,g={exports:{}},u={};function p(){return f||(f=1,function(e){var t="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var a in r)n(r,a)&&(e[a]=r[a])}}return e},e.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var r={arraySet:function(e,t,n,r,a){if(t.subarray&&e.subarray)e.set(t.subarray(n,n+r),a);else for(var i=0;i<r;i++)e[a+i]=t[n+i]},flattenChunks:function(e){var t,n,r,a,i,o;for(r=0,t=0,n=e.length;t<n;t++)r+=e[t].length;for(o=new Uint8Array(r),a=0,t=0,n=e.length;t<n;t++)i=e[t],o.set(i,a),a+=i.length;return o}},a={arraySet:function(e,t,n,r,a){for(var i=0;i<r;i++)e[a+i]=t[n+i]},flattenChunks:function(e){return[].concat.apply([],e)}};e.setTyped=function(t){t?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,r)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,a))},e.setTyped(t)}(u)),u}var b,m,w,_,v,y,C,k,x={},T={},I={};function E(){if(b)return I;b=1;var e=p();function t(e){for(var t=e.length;--t>=0;)e[t]=0}var n=256,r=286,a=30,i=15,o=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],s=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],l=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],c=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],d=new Array(576);t(d);var h=new Array(60);t(h);var f=new Array(512);t(f);var g=new Array(256);t(g);var u=new Array(29);t(u);var m,w,_,v=new Array(a);function y(e,t,n,r,a){this.static_tree=e,this.extra_bits=t,this.extra_base=n,this.elems=r,this.max_length=a,this.has_stree=e&&e.length}function C(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function k(e){return e<256?f[e]:f[256+(e>>>7)]}function x(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function T(e,t,n){e.bi_valid>16-n?(e.bi_buf|=t<<e.bi_valid&65535,x(e,e.bi_buf),e.bi_buf=t>>16-e.bi_valid,e.bi_valid+=n-16):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)}function E(e,t,n){T(e,n[2*t],n[2*t+1])}function S(e,t){var n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}function z(e,t,n){var r,a,o=new Array(16),s=0;for(r=1;r<=i;r++)o[r]=s=s+n[r-1]<<1;for(a=0;a<=t;a++){var l=e[2*a+1];0!==l&&(e[2*a]=S(o[l]++,l))}}function P(e){var t;for(t=0;t<r;t++)e.dyn_ltree[2*t]=0;for(t=0;t<a;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function R(e){e.bi_valid>8?x(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function D(e,t,n,r){var a=2*t,i=2*n;return e[a]<e[i]||e[a]===e[i]&&r[t]<=r[n]}function A(e,t,n){for(var r=e.heap[n],a=n<<1;a<=e.heap_len&&(a<e.heap_len&&D(t,e.heap[a+1],e.heap[a],e.depth)&&a++,!D(t,r,e.heap[a],e.depth));)e.heap[n]=e.heap[a],n=a,a<<=1;e.heap[n]=r}function $(e,t,r){var a,i,l,c,d=0;if(0!==e.last_lit)do{a=e.pending_buf[e.d_buf+2*d]<<8|e.pending_buf[e.d_buf+2*d+1],i=e.pending_buf[e.l_buf+d],d++,0===a?E(e,i,t):(E(e,(l=g[i])+n+1,t),0!==(c=o[l])&&T(e,i-=u[l],c),E(e,l=k(--a),r),0!==(c=s[l])&&T(e,a-=v[l],c))}while(d<e.last_lit);E(e,256,t)}function M(e,t){var n,r,a,o=t.dyn_tree,s=t.stat_desc.static_tree,l=t.stat_desc.has_stree,c=t.stat_desc.elems,d=-1;for(e.heap_len=0,e.heap_max=573,n=0;n<c;n++)0!==o[2*n]?(e.heap[++e.heap_len]=d=n,e.depth[n]=0):o[2*n+1]=0;for(;e.heap_len<2;)o[2*(a=e.heap[++e.heap_len]=d<2?++d:0)]=1,e.depth[a]=0,e.opt_len--,l&&(e.static_len-=s[2*a+1]);for(t.max_code=d,n=e.heap_len>>1;n>=1;n--)A(e,o,n);a=c;do{n=e.heap[1],e.heap[1]=e.heap[e.heap_len--],A(e,o,1),r=e.heap[1],e.heap[--e.heap_max]=n,e.heap[--e.heap_max]=r,o[2*a]=o[2*n]+o[2*r],e.depth[a]=(e.depth[n]>=e.depth[r]?e.depth[n]:e.depth[r])+1,o[2*n+1]=o[2*r+1]=a,e.heap[1]=a++,A(e,o,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var n,r,a,o,s,l,c=t.dyn_tree,d=t.max_code,h=t.stat_desc.static_tree,f=t.stat_desc.has_stree,g=t.stat_desc.extra_bits,u=t.stat_desc.extra_base,p=t.stat_desc.max_length,b=0;for(o=0;o<=i;o++)e.bl_count[o]=0;for(c[2*e.heap[e.heap_max]+1]=0,n=e.heap_max+1;n<573;n++)(o=c[2*c[2*(r=e.heap[n])+1]+1]+1)>p&&(o=p,b++),c[2*r+1]=o,r>d||(e.bl_count[o]++,s=0,r>=u&&(s=g[r-u]),l=c[2*r],e.opt_len+=l*(o+s),f&&(e.static_len+=l*(h[2*r+1]+s)));if(0!==b){do{for(o=p-1;0===e.bl_count[o];)o--;e.bl_count[o]--,e.bl_count[o+1]+=2,e.bl_count[p]--,b-=2}while(b>0);for(o=p;0!==o;o--)for(r=e.bl_count[o];0!==r;)(a=e.heap[--n])>d||(c[2*a+1]!==o&&(e.opt_len+=(o-c[2*a+1])*c[2*a],c[2*a+1]=o),r--)}}(e,t),z(o,d,e.bl_count)}function U(e,t,n){var r,a,i=-1,o=t[1],s=0,l=7,c=4;for(0===o&&(l=138,c=3),t[2*(n+1)+1]=65535,r=0;r<=n;r++)a=o,o=t[2*(r+1)+1],++s<l&&a===o||(s<c?e.bl_tree[2*a]+=s:0!==a?(a!==i&&e.bl_tree[2*a]++,e.bl_tree[32]++):s<=10?e.bl_tree[34]++:e.bl_tree[36]++,s=0,i=a,0===o?(l=138,c=3):a===o?(l=6,c=3):(l=7,c=4))}function B(e,t,n){var r,a,i=-1,o=t[1],s=0,l=7,c=4;for(0===o&&(l=138,c=3),r=0;r<=n;r++)if(a=o,o=t[2*(r+1)+1],!(++s<l&&a===o)){if(s<c)do{E(e,a,e.bl_tree)}while(0!==--s);else 0!==a?(a!==i&&(E(e,a,e.bl_tree),s--),E(e,16,e.bl_tree),T(e,s-3,2)):s<=10?(E(e,17,e.bl_tree),T(e,s-3,3)):(E(e,18,e.bl_tree),T(e,s-11,7));s=0,i=a,0===o?(l=138,c=3):a===o?(l=6,c=3):(l=7,c=4)}}t(v);var N=!1;function F(t,n,r,a){T(t,0+(a?1:0),3),function(t,n,r){R(t),x(t,r),x(t,~r),e.arraySet(t.pending_buf,t.window,n,r,t.pending),t.pending+=r}(t,n,r)}return I._tr_init=function(e){N||(!function(){var e,t,n,c,p,b=new Array(16);for(n=0,c=0;c<28;c++)for(u[c]=n,e=0;e<1<<o[c];e++)g[n++]=c;for(g[n-1]=c,p=0,c=0;c<16;c++)for(v[c]=p,e=0;e<1<<s[c];e++)f[p++]=c;for(p>>=7;c<a;c++)for(v[c]=p<<7,e=0;e<1<<s[c]-7;e++)f[256+p++]=c;for(t=0;t<=i;t++)b[t]=0;for(e=0;e<=143;)d[2*e+1]=8,e++,b[8]++;for(;e<=255;)d[2*e+1]=9,e++,b[9]++;for(;e<=279;)d[2*e+1]=7,e++,b[7]++;for(;e<=287;)d[2*e+1]=8,e++,b[8]++;for(z(d,287,b),e=0;e<a;e++)h[2*e+1]=5,h[2*e]=S(e,5);m=new y(d,o,257,r,i),w=new y(h,s,0,a,i),_=new y(new Array(0),l,0,19,7)}(),N=!0),e.l_desc=new C(e.dyn_ltree,m),e.d_desc=new C(e.dyn_dtree,w),e.bl_desc=new C(e.bl_tree,_),e.bi_buf=0,e.bi_valid=0,P(e)},I._tr_stored_block=F,I._tr_flush_block=function(e,t,r,a){var i,o,s=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,r=4093624447;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<n;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0}(e)),M(e,e.l_desc),M(e,e.d_desc),s=function(e){var t;for(U(e,e.dyn_ltree,e.l_desc.max_code),U(e,e.dyn_dtree,e.d_desc.max_code),M(e,e.bl_desc),t=18;t>=3&&0===e.bl_tree[2*c[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(o=e.static_len+3+7>>>3)<=i&&(i=o)):i=o=r+5,r+4<=i&&-1!==t?F(e,t,r,a):4===e.strategy||o===i?(T(e,2+(a?1:0),3),$(e,d,h)):(T(e,4+(a?1:0),3),function(e,t,n,r){var a;for(T(e,t-257,5),T(e,n-1,5),T(e,r-4,4),a=0;a<r;a++)T(e,e.bl_tree[2*c[a]+1],3);B(e,e.dyn_ltree,t-1),B(e,e.dyn_dtree,n-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,s+1),$(e,e.dyn_ltree,e.dyn_dtree)),P(e),a&&R(e)},I._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(g[r]+n+1)]++,e.dyn_dtree[2*k(t)]++),e.last_lit===e.lit_bufsize-1},I._tr_align=function(e){T(e,2,3),E(e,256,d),function(e){16===e.bi_valid?(x(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)},I}function S(){if(w)return m;return w=1,m=function(e,t,n,r){for(var a=65535&e,i=e>>>16&65535,o=0;0!==n;){n-=o=n>2e3?2e3:n;do{i=i+(a=a+t[r++]|0)|0}while(--o);a%=65521,i%=65521}return a|i<<16}}function z(){if(v)return _;v=1;var e=function(){for(var e,t=[],n=0;n<256;n++){e=n;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t}();return _=function(t,n,r,a){var i=e,o=a+r;t^=-1;for(var s=a;s<o;s++)t=t>>>8^i[255&(t^n[s])];return-1^t}}function P(){return C?y:(C=1,y={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"})}function R(){if(k)return T;k=1;var e,t=p(),n=E(),r=S(),a=z(),i=P(),o=-2,s=258,l=262,c=103,d=113,h=666;function f(e,t){return e.msg=i[t],t}function g(e){return(e<<1)-(e>4?9:0)}function u(e){for(var t=e.length;--t>=0;)e[t]=0}function b(e){var n=e.state,r=n.pending;r>e.avail_out&&(r=e.avail_out),0!==r&&(t.arraySet(e.output,n.pending_buf,n.pending_out,r,e.next_out),e.next_out+=r,n.pending_out+=r,e.total_out+=r,e.avail_out-=r,n.pending-=r,0===n.pending&&(n.pending_out=0))}function m(e,t){n._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,b(e.strm)}function w(e,t){e.pending_buf[e.pending++]=t}function _(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function v(e,n,i,o){var s=e.avail_in;return s>o&&(s=o),0===s?0:(e.avail_in-=s,t.arraySet(n,e.input,e.next_in,s,i),1===e.state.wrap?e.adler=r(e.adler,n,s,i):2===e.state.wrap&&(e.adler=a(e.adler,n,s,i)),e.next_in+=s,e.total_in+=s,s)}function y(e,t){var n,r,a=e.max_chain_length,i=e.strstart,o=e.prev_length,c=e.nice_match,d=e.strstart>e.w_size-l?e.strstart-(e.w_size-l):0,h=e.window,f=e.w_mask,g=e.prev,u=e.strstart+s,p=h[i+o-1],b=h[i+o];e.prev_length>=e.good_match&&(a>>=2),c>e.lookahead&&(c=e.lookahead);do{if(h[(n=t)+o]===b&&h[n+o-1]===p&&h[n]===h[i]&&h[++n]===h[i+1]){i+=2,n++;do{}while(h[++i]===h[++n]&&h[++i]===h[++n]&&h[++i]===h[++n]&&h[++i]===h[++n]&&h[++i]===h[++n]&&h[++i]===h[++n]&&h[++i]===h[++n]&&h[++i]===h[++n]&&i<u);if(r=s-(u-i),i=u-s,r>o){if(e.match_start=t,o=r,r>=c)break;p=h[i+o-1],b=h[i+o]}}}while((t=g[t&f])>d&&0!==--a);return o<=e.lookahead?o:e.lookahead}function C(e){var n,r,a,i,o,s=e.w_size;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=s+(s-l)){t.arraySet(e.window,e.window,s,s,0),e.match_start-=s,e.strstart-=s,e.block_start-=s,n=r=e.hash_size;do{a=e.head[--n],e.head[n]=a>=s?a-s:0}while(--r);n=r=s;do{a=e.prev[--n],e.prev[n]=a>=s?a-s:0}while(--r);i+=s}if(0===e.strm.avail_in)break;if(r=v(e.strm,e.window,e.strstart+e.lookahead,i),e.lookahead+=r,e.lookahead+e.insert>=3)for(o=e.strstart-e.insert,e.ins_h=e.window[o],e.ins_h=(e.ins_h<<e.hash_shift^e.window[o+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[o+3-1])&e.hash_mask,e.prev[o&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=o,o++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<l&&0!==e.strm.avail_in)}function x(e,t){for(var r,a;;){if(e.lookahead<l){if(C(e),e.lookahead<l&&0===t)return 1;if(0===e.lookahead)break}if(r=0,e.lookahead>=3&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-l&&(e.match_length=y(e,r)),e.match_length>=3)if(a=n._tr_tally(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do{e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!==--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else a=n._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(a&&(m(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<2?e.strstart:2,4===t?(m(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(m(e,!1),0===e.strm.avail_out)?1:2}function I(e,t){for(var r,a,i;;){if(e.lookahead<l){if(C(e),e.lookahead<l&&0===t)return 1;if(0===e.lookahead)break}if(r=0,e.lookahead>=3&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-l&&(e.match_length=y(e,r),e.match_length<=5&&(1===e.strategy||3===e.match_length&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-3,a=n._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!==--e.prev_length);if(e.match_available=0,e.match_length=2,e.strstart++,a&&(m(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if((a=n._tr_tally(e,0,e.window[e.strstart-1]))&&m(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(a=n._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,4===t?(m(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(m(e,!1),0===e.strm.avail_out)?1:2}function R(e,t,n,r,a){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=r,this.func=a}function D(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new t.Buf16(1146),this.dyn_dtree=new t.Buf16(122),this.bl_tree=new t.Buf16(78),u(this.dyn_ltree),u(this.dyn_dtree),u(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new t.Buf16(16),this.heap=new t.Buf16(573),u(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new t.Buf16(573),u(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function A(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=2,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?42:d,e.adler=2===t.wrap?0:1,t.last_flush=0,n._tr_init(t),0):f(e,o)}function $(t){var n,r=A(t);return 0===r&&((n=t.state).window_size=2*n.w_size,u(n.head),n.max_lazy_match=e[n.level].max_lazy,n.good_match=e[n.level].good_length,n.nice_match=e[n.level].nice_length,n.max_chain_length=e[n.level].max_chain,n.strstart=0,n.block_start=0,n.lookahead=0,n.insert=0,n.match_length=n.prev_length=2,n.match_available=0,n.ins_h=0),r}function M(e,n,r,a,i,s){if(!e)return o;var l=1;if(-1===n&&(n=6),a<0?(l=0,a=-a):a>15&&(l=2,a-=16),i<1||i>9||8!==r||a<8||a>15||n<0||n>9||s<0||s>4)return f(e,o);8===a&&(a=9);var c=new D;return e.state=c,c.strm=e,c.wrap=l,c.gzhead=null,c.w_bits=a,c.w_size=1<<c.w_bits,c.w_mask=c.w_size-1,c.hash_bits=i+7,c.hash_size=1<<c.hash_bits,c.hash_mask=c.hash_size-1,c.hash_shift=~~((c.hash_bits+3-1)/3),c.window=new t.Buf8(2*c.w_size),c.head=new t.Buf16(c.hash_size),c.prev=new t.Buf16(c.w_size),c.lit_bufsize=1<<i+6,c.pending_buf_size=4*c.lit_bufsize,c.pending_buf=new t.Buf8(c.pending_buf_size),c.d_buf=1*c.lit_bufsize,c.l_buf=3*c.lit_bufsize,c.level=n,c.strategy=s,c.method=r,$(e)}return e=[new R(0,0,0,0,function(e,t){var n=65535;for(n>e.pending_buf_size-5&&(n=e.pending_buf_size-5);;){if(e.lookahead<=1){if(C(e),0===e.lookahead&&0===t)return 1;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var r=e.block_start+n;if((0===e.strstart||e.strstart>=r)&&(e.lookahead=e.strstart-r,e.strstart=r,m(e,!1),0===e.strm.avail_out))return 1;if(e.strstart-e.block_start>=e.w_size-l&&(m(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(m(e,!0),0===e.strm.avail_out?3:4):(e.strstart>e.block_start&&(m(e,!1),e.strm.avail_out),1)}),new R(4,4,8,4,x),new R(4,5,16,8,x),new R(4,6,32,32,x),new R(4,4,16,16,I),new R(8,16,32,32,I),new R(8,16,128,128,I),new R(8,32,128,256,I),new R(32,128,258,1024,I),new R(32,258,258,4096,I)],T.deflateInit=function(e,t){return M(e,t,8,15,8,0)},T.deflateInit2=M,T.deflateReset=$,T.deflateResetKeep=A,T.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?o:(e.state.gzhead=t,0):o},T.deflate=function(t,r){var i,l,p,v;if(!t||!t.state||r>5||r<0)return t?f(t,o):o;if(l=t.state,!t.output||!t.input&&0!==t.avail_in||l.status===h&&4!==r)return f(t,0===t.avail_out?-5:o);if(l.strm=t,i=l.last_flush,l.last_flush=r,42===l.status)if(2===l.wrap)t.adler=0,w(l,31),w(l,139),w(l,8),l.gzhead?(w(l,(l.gzhead.text?1:0)+(l.gzhead.hcrc?2:0)+(l.gzhead.extra?4:0)+(l.gzhead.name?8:0)+(l.gzhead.comment?16:0)),w(l,255&l.gzhead.time),w(l,l.gzhead.time>>8&255),w(l,l.gzhead.time>>16&255),w(l,l.gzhead.time>>24&255),w(l,9===l.level?2:l.strategy>=2||l.level<2?4:0),w(l,255&l.gzhead.os),l.gzhead.extra&&l.gzhead.extra.length&&(w(l,255&l.gzhead.extra.length),w(l,l.gzhead.extra.length>>8&255)),l.gzhead.hcrc&&(t.adler=a(t.adler,l.pending_buf,l.pending,0)),l.gzindex=0,l.status=69):(w(l,0),w(l,0),w(l,0),w(l,0),w(l,0),w(l,9===l.level?2:l.strategy>=2||l.level<2?4:0),w(l,3),l.status=d);else{var y=8+(l.w_bits-8<<4)<<8;y|=(l.strategy>=2||l.level<2?0:l.level<6?1:6===l.level?2:3)<<6,0!==l.strstart&&(y|=32),y+=31-y%31,l.status=d,_(l,y),0!==l.strstart&&(_(l,t.adler>>>16),_(l,65535&t.adler)),t.adler=1}if(69===l.status)if(l.gzhead.extra){for(p=l.pending;l.gzindex<(65535&l.gzhead.extra.length)&&(l.pending!==l.pending_buf_size||(l.gzhead.hcrc&&l.pending>p&&(t.adler=a(t.adler,l.pending_buf,l.pending-p,p)),b(t),p=l.pending,l.pending!==l.pending_buf_size));)w(l,255&l.gzhead.extra[l.gzindex]),l.gzindex++;l.gzhead.hcrc&&l.pending>p&&(t.adler=a(t.adler,l.pending_buf,l.pending-p,p)),l.gzindex===l.gzhead.extra.length&&(l.gzindex=0,l.status=73)}else l.status=73;if(73===l.status)if(l.gzhead.name){p=l.pending;do{if(l.pending===l.pending_buf_size&&(l.gzhead.hcrc&&l.pending>p&&(t.adler=a(t.adler,l.pending_buf,l.pending-p,p)),b(t),p=l.pending,l.pending===l.pending_buf_size)){v=1;break}v=l.gzindex<l.gzhead.name.length?255&l.gzhead.name.charCodeAt(l.gzindex++):0,w(l,v)}while(0!==v);l.gzhead.hcrc&&l.pending>p&&(t.adler=a(t.adler,l.pending_buf,l.pending-p,p)),0===v&&(l.gzindex=0,l.status=91)}else l.status=91;if(91===l.status)if(l.gzhead.comment){p=l.pending;do{if(l.pending===l.pending_buf_size&&(l.gzhead.hcrc&&l.pending>p&&(t.adler=a(t.adler,l.pending_buf,l.pending-p,p)),b(t),p=l.pending,l.pending===l.pending_buf_size)){v=1;break}v=l.gzindex<l.gzhead.comment.length?255&l.gzhead.comment.charCodeAt(l.gzindex++):0,w(l,v)}while(0!==v);l.gzhead.hcrc&&l.pending>p&&(t.adler=a(t.adler,l.pending_buf,l.pending-p,p)),0===v&&(l.status=c)}else l.status=c;if(l.status===c&&(l.gzhead.hcrc?(l.pending+2>l.pending_buf_size&&b(t),l.pending+2<=l.pending_buf_size&&(w(l,255&t.adler),w(l,t.adler>>8&255),t.adler=0,l.status=d)):l.status=d),0!==l.pending){if(b(t),0===t.avail_out)return l.last_flush=-1,0}else if(0===t.avail_in&&g(r)<=g(i)&&4!==r)return f(t,-5);if(l.status===h&&0!==t.avail_in)return f(t,-5);if(0!==t.avail_in||0!==l.lookahead||0!==r&&l.status!==h){var k=2===l.strategy?function(e,t){for(var r;;){if(0===e.lookahead&&(C(e),0===e.lookahead)){if(0===t)return 1;break}if(e.match_length=0,r=n._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(m(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(m(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(m(e,!1),0===e.strm.avail_out)?1:2}(l,r):3===l.strategy?function(e,t){for(var r,a,i,o,l=e.window;;){if(e.lookahead<=s){if(C(e),e.lookahead<=s&&0===t)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=3&&e.strstart>0&&(a=l[i=e.strstart-1])===l[++i]&&a===l[++i]&&a===l[++i]){o=e.strstart+s;do{}while(a===l[++i]&&a===l[++i]&&a===l[++i]&&a===l[++i]&&a===l[++i]&&a===l[++i]&&a===l[++i]&&a===l[++i]&&i<o);e.match_length=s-(o-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=3?(r=n._tr_tally(e,1,e.match_length-3),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=n._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(m(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(m(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(m(e,!1),0===e.strm.avail_out)?1:2}(l,r):e[l.level].func(l,r);if(3!==k&&4!==k||(l.status=h),1===k||3===k)return 0===t.avail_out&&(l.last_flush=-1),0;if(2===k&&(1===r?n._tr_align(l):5!==r&&(n._tr_stored_block(l,0,0,!1),3===r&&(u(l.head),0===l.lookahead&&(l.strstart=0,l.block_start=0,l.insert=0))),b(t),0===t.avail_out))return l.last_flush=-1,0}return 4!==r?0:l.wrap<=0?1:(2===l.wrap?(w(l,255&t.adler),w(l,t.adler>>8&255),w(l,t.adler>>16&255),w(l,t.adler>>24&255),w(l,255&t.total_in),w(l,t.total_in>>8&255),w(l,t.total_in>>16&255),w(l,t.total_in>>24&255)):(_(l,t.adler>>>16),_(l,65535&t.adler)),b(t),l.wrap>0&&(l.wrap=-l.wrap),0!==l.pending?0:1)},T.deflateEnd=function(e){var t;return e&&e.state?42!==(t=e.state.status)&&69!==t&&73!==t&&91!==t&&t!==c&&t!==d&&t!==h?f(e,o):(e.state=null,t===d?f(e,-3):0):o},T.deflateSetDictionary=function(e,n){var a,i,s,l,c,d,h,f,g=n.length;if(!e||!e.state)return o;if(2===(l=(a=e.state).wrap)||1===l&&42!==a.status||a.lookahead)return o;for(1===l&&(e.adler=r(e.adler,n,g,0)),a.wrap=0,g>=a.w_size&&(0===l&&(u(a.head),a.strstart=0,a.block_start=0,a.insert=0),f=new t.Buf8(a.w_size),t.arraySet(f,n,g-a.w_size,a.w_size,0),n=f,g=a.w_size),c=e.avail_in,d=e.next_in,h=e.input,e.avail_in=g,e.next_in=0,e.input=n,C(a);a.lookahead>=3;){i=a.strstart,s=a.lookahead-2;do{a.ins_h=(a.ins_h<<a.hash_shift^a.window[i+3-1])&a.hash_mask,a.prev[i&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=i,i++}while(--s);a.strstart=i,a.lookahead=2,C(a)}return a.strstart+=a.lookahead,a.block_start=a.strstart,a.insert=a.lookahead,a.lookahead=0,a.match_length=a.prev_length=2,a.match_available=0,e.next_in=d,e.input=h,e.avail_in=c,a.wrap=l,0},T.deflateInfo="pako deflate (from Nodeca project)",T}var D,A,$,M,U={};function B(){if(D)return U;D=1;var e=p(),t=!0,n=!0;try{String.fromCharCode.apply(null,[0])}catch(o){t=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(o){n=!1}for(var r=new e.Buf8(256),a=0;a<256;a++)r[a]=a>=252?6:a>=248?5:a>=240?4:a>=224?3:a>=192?2:1;function i(r,a){if(a<65534&&(r.subarray&&n||!r.subarray&&t))return String.fromCharCode.apply(null,e.shrinkBuf(r,a));for(var i="",o=0;o<a;o++)i+=String.fromCharCode(r[o]);return i}return r[254]=r[254]=1,U.string2buf=function(t){var n,r,a,i,o,s=t.length,l=0;for(i=0;i<s;i++)55296==(64512&(r=t.charCodeAt(i)))&&i+1<s&&56320==(64512&(a=t.charCodeAt(i+1)))&&(r=65536+(r-55296<<10)+(a-56320),i++),l+=r<128?1:r<2048?2:r<65536?3:4;for(n=new e.Buf8(l),o=0,i=0;o<l;i++)55296==(64512&(r=t.charCodeAt(i)))&&i+1<s&&56320==(64512&(a=t.charCodeAt(i+1)))&&(r=65536+(r-55296<<10)+(a-56320),i++),r<128?n[o++]=r:r<2048?(n[o++]=192|r>>>6,n[o++]=128|63&r):r<65536?(n[o++]=224|r>>>12,n[o++]=128|r>>>6&63,n[o++]=128|63&r):(n[o++]=240|r>>>18,n[o++]=128|r>>>12&63,n[o++]=128|r>>>6&63,n[o++]=128|63&r);return n},U.buf2binstring=function(e){return i(e,e.length)},U.binstring2buf=function(t){for(var n=new e.Buf8(t.length),r=0,a=n.length;r<a;r++)n[r]=t.charCodeAt(r);return n},U.buf2string=function(e,t){var n,a,o,s,l=t||e.length,c=new Array(2*l);for(a=0,n=0;n<l;)if((o=e[n++])<128)c[a++]=o;else if((s=r[o])>4)c[a++]=65533,n+=s-1;else{for(o&=2===s?31:3===s?15:7;s>1&&n<l;)o=o<<6|63&e[n++],s--;s>1?c[a++]=65533:o<65536?c[a++]=o:(o-=65536,c[a++]=55296|o>>10&1023,c[a++]=56320|1023&o)}return i(c,a)},U.utf8border=function(e,t){var n;for((t=t||e.length)>e.length&&(t=e.length),n=t-1;n>=0&&128==(192&e[n]);)n--;return n<0||0===n?t:n+r[e[n]]>t?n:t},U}function N(){if($)return A;return $=1,A=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}}var F,O,W,L,G,Z,H,j,q,K,Y,V,J,X,Q,ee,te,ne={},re={};function ae(){if(G)return re;G=1;var e=p(),t=S(),n=z(),r=O?F:(O=1,F=function(e,t){var n,r,a,i,o,s,l,c,d,h,f,g,u,p,b,m,w,_,v,y,C,k,x,T,I;n=e.state,r=e.next_in,T=e.input,a=r+(e.avail_in-5),i=e.next_out,I=e.output,o=i-(t-e.avail_out),s=i+(e.avail_out-257),l=n.dmax,c=n.wsize,d=n.whave,h=n.wnext,f=n.window,g=n.hold,u=n.bits,p=n.lencode,b=n.distcode,m=(1<<n.lenbits)-1,w=(1<<n.distbits)-1;e:do{u<15&&(g+=T[r++]<<u,u+=8,g+=T[r++]<<u,u+=8),_=p[g&m];t:for(;;){if(g>>>=v=_>>>24,u-=v,0==(v=_>>>16&255))I[i++]=65535&_;else{if(!(16&v)){if(64&v){if(32&v){n.mode=12;break e}e.msg="invalid literal/length code",n.mode=30;break e}_=p[(65535&_)+(g&(1<<v)-1)];continue t}for(y=65535&_,(v&=15)&&(u<v&&(g+=T[r++]<<u,u+=8),y+=g&(1<<v)-1,g>>>=v,u-=v),u<15&&(g+=T[r++]<<u,u+=8,g+=T[r++]<<u,u+=8),_=b[g&w];;){if(g>>>=v=_>>>24,u-=v,16&(v=_>>>16&255)){if(C=65535&_,u<(v&=15)&&(g+=T[r++]<<u,(u+=8)<v&&(g+=T[r++]<<u,u+=8)),(C+=g&(1<<v)-1)>l){e.msg="invalid distance too far back",n.mode=30;break e}if(g>>>=v,u-=v,C>(v=i-o)){if((v=C-v)>d&&n.sane){e.msg="invalid distance too far back",n.mode=30;break e}if(k=0,x=f,0===h){if(k+=c-v,v<y){y-=v;do{I[i++]=f[k++]}while(--v);k=i-C,x=I}}else if(h<v){if(k+=c+h-v,(v-=h)<y){y-=v;do{I[i++]=f[k++]}while(--v);if(k=0,h<y){y-=v=h;do{I[i++]=f[k++]}while(--v);k=i-C,x=I}}}else if(k+=h-v,v<y){y-=v;do{I[i++]=f[k++]}while(--v);k=i-C,x=I}for(;y>2;)I[i++]=x[k++],I[i++]=x[k++],I[i++]=x[k++],y-=3;y&&(I[i++]=x[k++],y>1&&(I[i++]=x[k++]))}else{k=i-C;do{I[i++]=I[k++],I[i++]=I[k++],I[i++]=I[k++],y-=3}while(y>2);y&&(I[i++]=I[k++],y>1&&(I[i++]=I[k++]))}break}if(64&v){e.msg="invalid distance code",n.mode=30;break e}_=b[(65535&_)+(g&(1<<v)-1)]}}break}}while(r<a&&i<s);r-=y=u>>3,g&=(1<<(u-=y<<3))-1,e.next_in=r,e.next_out=i,e.avail_in=r<a?a-r+5:5-(r-a),e.avail_out=i<s?s-i+257:257-(i-s),n.hold=g,n.bits=u}),a=function(){if(L)return W;L=1;var e=p(),t=15,n=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],r=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],a=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],i=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];return W=function(o,s,l,c,d,h,f,g){var u,p,b,m,w,_,v,y,C,k=g.bits,x=0,T=0,I=0,E=0,S=0,z=0,P=0,R=0,D=0,A=0,$=null,M=0,U=new e.Buf16(16),B=new e.Buf16(16),N=null,F=0;for(x=0;x<=t;x++)U[x]=0;for(T=0;T<c;T++)U[s[l+T]]++;for(S=k,E=t;E>=1&&0===U[E];E--);if(S>E&&(S=E),0===E)return d[h++]=20971520,d[h++]=20971520,g.bits=1,0;for(I=1;I<E&&0===U[I];I++);for(S<I&&(S=I),R=1,x=1;x<=t;x++)if(R<<=1,(R-=U[x])<0)return-1;if(R>0&&(0===o||1!==E))return-1;for(B[1]=0,x=1;x<t;x++)B[x+1]=B[x]+U[x];for(T=0;T<c;T++)0!==s[l+T]&&(f[B[s[l+T]]++]=T);if(0===o?($=N=f,_=19):1===o?($=n,M-=257,N=r,F-=257,_=256):($=a,N=i,_=-1),A=0,T=0,x=I,w=h,z=S,P=0,b=-1,m=(D=1<<S)-1,1===o&&D>852||2===o&&D>592)return 1;for(;;){v=x-P,f[T]<_?(y=0,C=f[T]):f[T]>_?(y=N[F+f[T]],C=$[M+f[T]]):(y=96,C=0),u=1<<x-P,I=p=1<<z;do{d[w+(A>>P)+(p-=u)]=v<<24|y<<16|C}while(0!==p);for(u=1<<x-1;A&u;)u>>=1;if(0!==u?(A&=u-1,A+=u):A=0,T++,0===--U[x]){if(x===E)break;x=s[l+f[T]]}if(x>S&&(A&m)!==b){for(0===P&&(P=S),w+=I,R=1<<(z=x-P);z+P<E&&!((R-=U[z+P])<=0);)z++,R<<=1;if(D+=1<<z,1===o&&D>852||2===o&&D>592)return 1;d[b=A&m]=S<<24|z<<16|w-h}}return 0!==A&&(d[w+A]=x-P<<24|64<<16),g.bits=S,0}}(),i=-2,o=12,s=30;function l(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function c(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new e.Buf16(320),this.work=new e.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function d(t){var n;return t&&t.state?(n=t.state,t.total_in=t.total_out=n.total=0,t.msg="",n.wrap&&(t.adler=1&n.wrap),n.mode=1,n.last=0,n.havedict=0,n.dmax=32768,n.head=null,n.hold=0,n.bits=0,n.lencode=n.lendyn=new e.Buf32(852),n.distcode=n.distdyn=new e.Buf32(592),n.sane=1,n.back=-1,0):i}function h(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,d(e)):i}function f(e,t){var n,r;return e&&e.state?(r=e.state,t<0?(n=0,t=-t):(n=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?i:(null!==r.window&&r.wbits!==t&&(r.window=null),r.wrap=n,r.wbits=t,h(e))):i}function g(e,t){var n,r;return e?(r=new c,e.state=r,r.window=null,0!==(n=f(e,t))&&(e.state=null),n):i}var u,b,m=!0;function w(t){if(m){var n;for(u=new e.Buf32(512),b=new e.Buf32(32),n=0;n<144;)t.lens[n++]=8;for(;n<256;)t.lens[n++]=9;for(;n<280;)t.lens[n++]=7;for(;n<288;)t.lens[n++]=8;for(a(1,t.lens,0,288,u,0,t.work,{bits:9}),n=0;n<32;)t.lens[n++]=5;a(2,t.lens,0,32,b,0,t.work,{bits:5}),m=!1}t.lencode=u,t.lenbits=9,t.distcode=b,t.distbits=5}function _(t,n,r,a){var i,o=t.state;return null===o.window&&(o.wsize=1<<o.wbits,o.wnext=0,o.whave=0,o.window=new e.Buf8(o.wsize)),a>=o.wsize?(e.arraySet(o.window,n,r-o.wsize,o.wsize,0),o.wnext=0,o.whave=o.wsize):((i=o.wsize-o.wnext)>a&&(i=a),e.arraySet(o.window,n,r-a,i,o.wnext),(a-=i)?(e.arraySet(o.window,n,r-a,a,0),o.wnext=a,o.whave=o.wsize):(o.wnext+=i,o.wnext===o.wsize&&(o.wnext=0),o.whave<o.wsize&&(o.whave+=i))),0}return re.inflateReset=h,re.inflateReset2=f,re.inflateResetKeep=d,re.inflateInit=function(e){return g(e,15)},re.inflateInit2=g,re.inflate=function(c,d){var h,f,g,u,p,b,m,v,y,C,k,x,T,I,E,S,z,P,R,D,A,$,M,U,B=0,N=new e.Buf8(4),F=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!c||!c.state||!c.output||!c.input&&0!==c.avail_in)return i;(h=c.state).mode===o&&(h.mode=13),p=c.next_out,g=c.output,m=c.avail_out,u=c.next_in,f=c.input,b=c.avail_in,v=h.hold,y=h.bits,C=b,k=m,$=0;e:for(;;)switch(h.mode){case 1:if(0===h.wrap){h.mode=13;break}for(;y<16;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}if(2&h.wrap&&35615===v){h.check=0,N[0]=255&v,N[1]=v>>>8&255,h.check=n(h.check,N,2,0),v=0,y=0,h.mode=2;break}if(h.flags=0,h.head&&(h.head.done=!1),!(1&h.wrap)||(((255&v)<<8)+(v>>8))%31){c.msg="incorrect header check",h.mode=s;break}if(8!=(15&v)){c.msg="unknown compression method",h.mode=s;break}if(y-=4,A=8+(15&(v>>>=4)),0===h.wbits)h.wbits=A;else if(A>h.wbits){c.msg="invalid window size",h.mode=s;break}h.dmax=1<<A,c.adler=h.check=1,h.mode=512&v?10:o,v=0,y=0;break;case 2:for(;y<16;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}if(h.flags=v,8!=(255&h.flags)){c.msg="unknown compression method",h.mode=s;break}if(57344&h.flags){c.msg="unknown header flags set",h.mode=s;break}h.head&&(h.head.text=v>>8&1),512&h.flags&&(N[0]=255&v,N[1]=v>>>8&255,h.check=n(h.check,N,2,0)),v=0,y=0,h.mode=3;case 3:for(;y<32;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}h.head&&(h.head.time=v),512&h.flags&&(N[0]=255&v,N[1]=v>>>8&255,N[2]=v>>>16&255,N[3]=v>>>24&255,h.check=n(h.check,N,4,0)),v=0,y=0,h.mode=4;case 4:for(;y<16;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}h.head&&(h.head.xflags=255&v,h.head.os=v>>8),512&h.flags&&(N[0]=255&v,N[1]=v>>>8&255,h.check=n(h.check,N,2,0)),v=0,y=0,h.mode=5;case 5:if(1024&h.flags){for(;y<16;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}h.length=v,h.head&&(h.head.extra_len=v),512&h.flags&&(N[0]=255&v,N[1]=v>>>8&255,h.check=n(h.check,N,2,0)),v=0,y=0}else h.head&&(h.head.extra=null);h.mode=6;case 6:if(1024&h.flags&&((x=h.length)>b&&(x=b),x&&(h.head&&(A=h.head.extra_len-h.length,h.head.extra||(h.head.extra=new Array(h.head.extra_len)),e.arraySet(h.head.extra,f,u,x,A)),512&h.flags&&(h.check=n(h.check,f,x,u)),b-=x,u+=x,h.length-=x),h.length))break e;h.length=0,h.mode=7;case 7:if(2048&h.flags){if(0===b)break e;x=0;do{A=f[u+x++],h.head&&A&&h.length<65536&&(h.head.name+=String.fromCharCode(A))}while(A&&x<b);if(512&h.flags&&(h.check=n(h.check,f,x,u)),b-=x,u+=x,A)break e}else h.head&&(h.head.name=null);h.length=0,h.mode=8;case 8:if(4096&h.flags){if(0===b)break e;x=0;do{A=f[u+x++],h.head&&A&&h.length<65536&&(h.head.comment+=String.fromCharCode(A))}while(A&&x<b);if(512&h.flags&&(h.check=n(h.check,f,x,u)),b-=x,u+=x,A)break e}else h.head&&(h.head.comment=null);h.mode=9;case 9:if(512&h.flags){for(;y<16;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}if(v!==(65535&h.check)){c.msg="header crc mismatch",h.mode=s;break}v=0,y=0}h.head&&(h.head.hcrc=h.flags>>9&1,h.head.done=!0),c.adler=h.check=0,h.mode=o;break;case 10:for(;y<32;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}c.adler=h.check=l(v),v=0,y=0,h.mode=11;case 11:if(0===h.havedict)return c.next_out=p,c.avail_out=m,c.next_in=u,c.avail_in=b,h.hold=v,h.bits=y,2;c.adler=h.check=1,h.mode=o;case o:if(5===d||6===d)break e;case 13:if(h.last){v>>>=7&y,y-=7&y,h.mode=27;break}for(;y<3;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}switch(h.last=1&v,y-=1,3&(v>>>=1)){case 0:h.mode=14;break;case 1:if(w(h),h.mode=20,6===d){v>>>=2,y-=2;break e}break;case 2:h.mode=17;break;case 3:c.msg="invalid block type",h.mode=s}v>>>=2,y-=2;break;case 14:for(v>>>=7&y,y-=7&y;y<32;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}if((65535&v)!=(v>>>16^65535)){c.msg="invalid stored block lengths",h.mode=s;break}if(h.length=65535&v,v=0,y=0,h.mode=15,6===d)break e;case 15:h.mode=16;case 16:if(x=h.length){if(x>b&&(x=b),x>m&&(x=m),0===x)break e;e.arraySet(g,f,u,x,p),b-=x,u+=x,m-=x,p+=x,h.length-=x;break}h.mode=o;break;case 17:for(;y<14;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}if(h.nlen=257+(31&v),v>>>=5,y-=5,h.ndist=1+(31&v),v>>>=5,y-=5,h.ncode=4+(15&v),v>>>=4,y-=4,h.nlen>286||h.ndist>30){c.msg="too many length or distance symbols",h.mode=s;break}h.have=0,h.mode=18;case 18:for(;h.have<h.ncode;){for(;y<3;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}h.lens[F[h.have++]]=7&v,v>>>=3,y-=3}for(;h.have<19;)h.lens[F[h.have++]]=0;if(h.lencode=h.lendyn,h.lenbits=7,M={bits:h.lenbits},$=a(0,h.lens,0,19,h.lencode,0,h.work,M),h.lenbits=M.bits,$){c.msg="invalid code lengths set",h.mode=s;break}h.have=0,h.mode=19;case 19:for(;h.have<h.nlen+h.ndist;){for(;S=(B=h.lencode[v&(1<<h.lenbits)-1])>>>16&255,z=65535&B,!((E=B>>>24)<=y);){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}if(z<16)v>>>=E,y-=E,h.lens[h.have++]=z;else{if(16===z){for(U=E+2;y<U;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}if(v>>>=E,y-=E,0===h.have){c.msg="invalid bit length repeat",h.mode=s;break}A=h.lens[h.have-1],x=3+(3&v),v>>>=2,y-=2}else if(17===z){for(U=E+3;y<U;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}y-=E,A=0,x=3+(7&(v>>>=E)),v>>>=3,y-=3}else{for(U=E+7;y<U;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}y-=E,A=0,x=11+(127&(v>>>=E)),v>>>=7,y-=7}if(h.have+x>h.nlen+h.ndist){c.msg="invalid bit length repeat",h.mode=s;break}for(;x--;)h.lens[h.have++]=A}}if(h.mode===s)break;if(0===h.lens[256]){c.msg="invalid code -- missing end-of-block",h.mode=s;break}if(h.lenbits=9,M={bits:h.lenbits},$=a(1,h.lens,0,h.nlen,h.lencode,0,h.work,M),h.lenbits=M.bits,$){c.msg="invalid literal/lengths set",h.mode=s;break}if(h.distbits=6,h.distcode=h.distdyn,M={bits:h.distbits},$=a(2,h.lens,h.nlen,h.ndist,h.distcode,0,h.work,M),h.distbits=M.bits,$){c.msg="invalid distances set",h.mode=s;break}if(h.mode=20,6===d)break e;case 20:h.mode=21;case 21:if(b>=6&&m>=258){c.next_out=p,c.avail_out=m,c.next_in=u,c.avail_in=b,h.hold=v,h.bits=y,r(c,k),p=c.next_out,g=c.output,m=c.avail_out,u=c.next_in,f=c.input,b=c.avail_in,v=h.hold,y=h.bits,h.mode===o&&(h.back=-1);break}for(h.back=0;S=(B=h.lencode[v&(1<<h.lenbits)-1])>>>16&255,z=65535&B,!((E=B>>>24)<=y);){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}if(S&&!(240&S)){for(P=E,R=S,D=z;S=(B=h.lencode[D+((v&(1<<P+R)-1)>>P)])>>>16&255,z=65535&B,!(P+(E=B>>>24)<=y);){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}v>>>=P,y-=P,h.back+=P}if(v>>>=E,y-=E,h.back+=E,h.length=z,0===S){h.mode=26;break}if(32&S){h.back=-1,h.mode=o;break}if(64&S){c.msg="invalid literal/length code",h.mode=s;break}h.extra=15&S,h.mode=22;case 22:if(h.extra){for(U=h.extra;y<U;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}h.length+=v&(1<<h.extra)-1,v>>>=h.extra,y-=h.extra,h.back+=h.extra}h.was=h.length,h.mode=23;case 23:for(;S=(B=h.distcode[v&(1<<h.distbits)-1])>>>16&255,z=65535&B,!((E=B>>>24)<=y);){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}if(!(240&S)){for(P=E,R=S,D=z;S=(B=h.distcode[D+((v&(1<<P+R)-1)>>P)])>>>16&255,z=65535&B,!(P+(E=B>>>24)<=y);){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}v>>>=P,y-=P,h.back+=P}if(v>>>=E,y-=E,h.back+=E,64&S){c.msg="invalid distance code",h.mode=s;break}h.offset=z,h.extra=15&S,h.mode=24;case 24:if(h.extra){for(U=h.extra;y<U;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}h.offset+=v&(1<<h.extra)-1,v>>>=h.extra,y-=h.extra,h.back+=h.extra}if(h.offset>h.dmax){c.msg="invalid distance too far back",h.mode=s;break}h.mode=25;case 25:if(0===m)break e;if(x=k-m,h.offset>x){if((x=h.offset-x)>h.whave&&h.sane){c.msg="invalid distance too far back",h.mode=s;break}x>h.wnext?(x-=h.wnext,T=h.wsize-x):T=h.wnext-x,x>h.length&&(x=h.length),I=h.window}else I=g,T=p-h.offset,x=h.length;x>m&&(x=m),m-=x,h.length-=x;do{g[p++]=I[T++]}while(--x);0===h.length&&(h.mode=21);break;case 26:if(0===m)break e;g[p++]=h.length,m--,h.mode=21;break;case 27:if(h.wrap){for(;y<32;){if(0===b)break e;b--,v|=f[u++]<<y,y+=8}if(k-=m,c.total_out+=k,h.total+=k,k&&(c.adler=h.check=h.flags?n(h.check,g,k,p-k):t(h.check,g,k,p-k)),k=m,(h.flags?v:l(v))!==h.check){c.msg="incorrect data check",h.mode=s;break}v=0,y=0}h.mode=28;case 28:if(h.wrap&&h.flags){for(;y<32;){if(0===b)break e;b--,v+=f[u++]<<y,y+=8}if(v!==(4294967295&h.total)){c.msg="incorrect length check",h.mode=s;break}v=0,y=0}h.mode=29;case 29:$=1;break e;case s:$=-3;break e;case 31:return-4;default:return i}return c.next_out=p,c.avail_out=m,c.next_in=u,c.avail_in=b,h.hold=v,h.bits=y,(h.wsize||k!==c.avail_out&&h.mode<s&&(h.mode<27||4!==d))&&_(c,c.output,c.next_out,k-c.avail_out),C-=c.avail_in,k-=c.avail_out,c.total_in+=C,c.total_out+=k,h.total+=k,h.wrap&&k&&(c.adler=h.check=h.flags?n(h.check,g,k,c.next_out-k):t(h.check,g,k,c.next_out-k)),c.data_type=h.bits+(h.last?64:0)+(h.mode===o?128:0)+(20===h.mode||15===h.mode?256:0),(0===C&&0===k||4===d)&&0===$&&($=-5),$},re.inflateEnd=function(e){if(!e||!e.state)return i;var t=e.state;return t.window&&(t.window=null),e.state=null,0},re.inflateGetHeader=function(e,t){var n;return e&&e.state&&2&(n=e.state).wrap?(n.head=t,t.done=!1,0):i},re.inflateSetDictionary=function(e,n){var r,a=n.length;return e&&e.state?0!==(r=e.state).wrap&&11!==r.mode?i:11===r.mode&&t(1,n,a,0)!==r.check?-3:_(e,n,a,a)?(r.mode=31,-4):(r.havedict=1,0):i},re.inflateInfo="pako inflate (from Nodeca project)",re}function ie(){return H?Z:(H=1,Z={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8})}function oe(){if(K)return ne;K=1;var e=ae(),t=p(),n=B(),r=ie(),a=P(),i=N(),o=q?j:(q=1,j=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}),s=Object.prototype.toString;function l(c){if(!(this instanceof l))return new l(c);this.options=t.assign({chunkSize:16384,windowBits:0,to:""},c||{});var d=this.options;d.raw&&d.windowBits>=0&&d.windowBits<16&&(d.windowBits=-d.windowBits,0===d.windowBits&&(d.windowBits=-15)),!(d.windowBits>=0&&d.windowBits<16)||c&&c.windowBits||(d.windowBits+=32),d.windowBits>15&&d.windowBits<48&&(15&d.windowBits||(d.windowBits|=15)),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new i,this.strm.avail_out=0;var h=e.inflateInit2(this.strm,d.windowBits);if(h!==r.Z_OK)throw new Error(a[h]);if(this.header=new o,e.inflateGetHeader(this.strm,this.header),d.dictionary&&("string"==typeof d.dictionary?d.dictionary=n.string2buf(d.dictionary):"[object ArrayBuffer]"===s.call(d.dictionary)&&(d.dictionary=new Uint8Array(d.dictionary)),d.raw&&(h=e.inflateSetDictionary(this.strm,d.dictionary))!==r.Z_OK))throw new Error(a[h])}function c(e,t){var n=new l(t);if(n.push(e,!0),n.err)throw n.msg||a[n.err];return n.result}return l.prototype.push=function(a,i){var o,l,c,d,h,f=this.strm,g=this.options.chunkSize,u=this.options.dictionary,p=!1;if(this.ended)return!1;l=i===~~i?i:!0===i?r.Z_FINISH:r.Z_NO_FLUSH,"string"==typeof a?f.input=n.binstring2buf(a):"[object ArrayBuffer]"===s.call(a)?f.input=new Uint8Array(a):f.input=a,f.next_in=0,f.avail_in=f.input.length;do{if(0===f.avail_out&&(f.output=new t.Buf8(g),f.next_out=0,f.avail_out=g),(o=e.inflate(f,r.Z_NO_FLUSH))===r.Z_NEED_DICT&&u&&(o=e.inflateSetDictionary(this.strm,u)),o===r.Z_BUF_ERROR&&!0===p&&(o=r.Z_OK,p=!1),o!==r.Z_STREAM_END&&o!==r.Z_OK)return this.onEnd(o),this.ended=!0,!1;f.next_out&&(0!==f.avail_out&&o!==r.Z_STREAM_END&&(0!==f.avail_in||l!==r.Z_FINISH&&l!==r.Z_SYNC_FLUSH)||("string"===this.options.to?(c=n.utf8border(f.output,f.next_out),d=f.next_out-c,h=n.buf2string(f.output,c),f.next_out=d,f.avail_out=g-d,d&&t.arraySet(f.output,f.output,c,d,0),this.onData(h)):this.onData(t.shrinkBuf(f.output,f.next_out)))),0===f.avail_in&&0===f.avail_out&&(p=!0)}while((f.avail_in>0||0===f.avail_out)&&o!==r.Z_STREAM_END);return o===r.Z_STREAM_END&&(l=r.Z_FINISH),l===r.Z_FINISH?(o=e.inflateEnd(this.strm),this.onEnd(o),this.ended=!0,o===r.Z_OK):l!==r.Z_SYNC_FLUSH||(this.onEnd(r.Z_OK),f.avail_out=0,!0)},l.prototype.onData=function(e){this.chunks.push(e)},l.prototype.onEnd=function(e){e===r.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=t.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},ne.Inflate=l,ne.inflate=c,ne.inflateRaw=function(e,t){return(t=t||{}).raw=!0,c(e,t)},ne.ungzip=c,ne}function se(){if(V)return Y;V=1;var e={};return(0,p().assign)(e,function(){if(M)return x;M=1;var e=R(),t=p(),n=B(),r=P(),a=N(),i=Object.prototype.toString;function o(s){if(!(this instanceof o))return new o(s);this.options=t.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},s||{});var l=this.options;l.raw&&l.windowBits>0?l.windowBits=-l.windowBits:l.gzip&&l.windowBits>0&&l.windowBits<16&&(l.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new a,this.strm.avail_out=0;var c=e.deflateInit2(this.strm,l.level,l.method,l.windowBits,l.memLevel,l.strategy);if(0!==c)throw new Error(r[c]);if(l.header&&e.deflateSetHeader(this.strm,l.header),l.dictionary){var d;if(d="string"==typeof l.dictionary?n.string2buf(l.dictionary):"[object ArrayBuffer]"===i.call(l.dictionary)?new Uint8Array(l.dictionary):l.dictionary,0!==(c=e.deflateSetDictionary(this.strm,d)))throw new Error(r[c]);this._dict_set=!0}}function s(e,t){var n=new o(t);if(n.push(e,!0),n.err)throw n.msg||r[n.err];return n.result}return o.prototype.push=function(r,a){var o,s,l=this.strm,c=this.options.chunkSize;if(this.ended)return!1;s=a===~~a?a:!0===a?4:0,"string"==typeof r?l.input=n.string2buf(r):"[object ArrayBuffer]"===i.call(r)?l.input=new Uint8Array(r):l.input=r,l.next_in=0,l.avail_in=l.input.length;do{if(0===l.avail_out&&(l.output=new t.Buf8(c),l.next_out=0,l.avail_out=c),1!==(o=e.deflate(l,s))&&0!==o)return this.onEnd(o),this.ended=!0,!1;0!==l.avail_out&&(0!==l.avail_in||4!==s&&2!==s)||("string"===this.options.to?this.onData(n.buf2binstring(t.shrinkBuf(l.output,l.next_out))):this.onData(t.shrinkBuf(l.output,l.next_out)))}while((l.avail_in>0||0===l.avail_out)&&1!==o);return 4===s?(o=e.deflateEnd(this.strm),this.onEnd(o),this.ended=!0,0===o):2!==s||(this.onEnd(0),l.avail_out=0,!0)},o.prototype.onData=function(e){this.chunks.push(e)},o.prototype.onEnd=function(e){0===e&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=t.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},x.Deflate=o,x.deflate=s,x.deflateRaw=function(e,t){return(t=t||{}).raw=!0,s(e,t)},x.gzip=function(e,t){return(t=t||{}).gzip=!0,s(e,t)},x}(),oe(),ie()),Y=e}J||(J=1,g.exports=te={},X=se(),ee=X,(Q=te).toRGBA8=function(e){var t=e.width,n=e.height;if(null==e.tabs.acTL)return[Q.toRGBA8.decodeImage(e.data,t,n,e).buffer];var r=[];null==e.frames[0].data&&(e.frames[0].data=e.data);for(var a,i=new Uint8Array(t*n*4),o=0;o<e.frames.length;o++){var s=e.frames[o],l=s.rect.x,c=s.rect.y,d=s.rect.width,h=s.rect.height,f=Q.toRGBA8.decodeImage(s.data,d,h,e);if(0==o?a=f:0==s.blend?Q._copyTile(f,d,h,a,t,n,l,c,0):1==s.blend&&Q._copyTile(f,d,h,a,t,n,l,c,1),r.push(a.buffer),a=a.slice(0),0==s.dispose);else if(1==s.dispose)Q._copyTile(i,d,h,a,t,n,l,c,0);else if(2==s.dispose){for(var g=o-1;2==e.frames[g].dispose;)g--;a=new Uint8Array(r[g]).slice(0)}}return r},Q.toRGBA8.decodeImage=function(e,t,n,r){var a=t*n,i=Q.decode._getBPP(r),o=Math.ceil(t*i/8),s=new Uint8Array(4*a),l=new Uint32Array(s.buffer),c=r.ctype,d=r.depth,h=Q._bin.readUshort;if(6==c){var f=a<<2;if(8==d)for(var g=0;g<f;g++)s[g]=e[g];if(16==d)for(g=0;g<f;g++)s[g]=e[g<<1]}else if(2==c){var u=r.tabs.tRNS,p=-1,b=-1,m=-1;if(u&&(p=u[0],b=u[1],m=u[2]),8==d)for(g=0;g<a;g++){var w=3*g;s[T=g<<2]=e[w],s[T+1]=e[w+1],s[T+2]=e[w+2],s[T+3]=255,-1!=p&&e[w]==p&&e[w+1]==b&&e[w+2]==m&&(s[T+3]=0)}if(16==d)for(g=0;g<a;g++)w=6*g,s[T=g<<2]=e[w],s[T+1]=e[w+2],s[T+2]=e[w+4],s[T+3]=255,-1!=p&&h(e,w)==p&&h(e,w+2)==b&&h(e,w+4)==m&&(s[T+3]=0)}else if(3==c){var _=r.tabs.PLTE,v=r.tabs.tRNS,y=v?v.length:0;if(1==d)for(var C=0;C<n;C++){var k=C*o,x=C*t;for(g=0;g<t;g++){var T=x+g<<2,I=3*(E=e[k+(g>>3)]>>7-(7&g)&1);s[T]=_[I],s[T+1]=_[I+1],s[T+2]=_[I+2],s[T+3]=E<y?v[E]:255}}if(2==d)for(C=0;C<n;C++)for(k=C*o,x=C*t,g=0;g<t;g++)T=x+g<<2,I=3*(E=e[k+(g>>2)]>>6-((3&g)<<1)&3),s[T]=_[I],s[T+1]=_[I+1],s[T+2]=_[I+2],s[T+3]=E<y?v[E]:255;if(4==d)for(C=0;C<n;C++)for(k=C*o,x=C*t,g=0;g<t;g++)T=x+g<<2,I=3*(E=e[k+(g>>1)]>>4-((1&g)<<2)&15),s[T]=_[I],s[T+1]=_[I+1],s[T+2]=_[I+2],s[T+3]=E<y?v[E]:255;if(8==d)for(g=0;g<a;g++){var E;T=g<<2,I=3*(E=e[g]),s[T]=_[I],s[T+1]=_[I+1],s[T+2]=_[I+2],s[T+3]=E<y?v[E]:255}}else if(4==c){if(8==d)for(g=0;g<a;g++){T=g<<2;var S=e[z=g<<1];s[T]=S,s[T+1]=S,s[T+2]=S,s[T+3]=e[z+1]}if(16==d)for(g=0;g<a;g++){var z;T=g<<2,S=e[z=g<<2],s[T]=S,s[T+1]=S,s[T+2]=S,s[T+3]=e[z+2]}}else if(0==c){if(p=r.tabs.tRNS?r.tabs.tRNS:-1,1==d)for(g=0;g<a;g++){var P=(S=255*(e[g>>3]>>7-(7&g)&1))==255*p?0:255;l[g]=P<<24|S<<16|S<<8|S}if(2==d)for(g=0;g<a;g++)P=(S=85*(e[g>>2]>>6-((3&g)<<1)&3))==85*p?0:255,l[g]=P<<24|S<<16|S<<8|S;if(4==d)for(g=0;g<a;g++)P=(S=17*(e[g>>1]>>4-((1&g)<<2)&15))==17*p?0:255,l[g]=P<<24|S<<16|S<<8|S;if(8==d)for(g=0;g<a;g++)P=(S=e[g])==p?0:255,l[g]=P<<24|S<<16|S<<8|S;if(16==d)for(g=0;g<a;g++)S=e[g<<1],P=h(e,g<<1)==p?0:255,l[g]=P<<24|S<<16|S<<8|S}return s},Q.decode=function(e){for(var t,n=new Uint8Array(e),r=8,a=Q._bin,i=a.readUshort,o=a.readUint,s={tabs:{},frames:[]},l=new Uint8Array(n.length),c=0,d=0,h=[137,80,78,71,13,10,26,10],f=0;f<8;f++)if(n[f]!=h[f])throw"The input is not a PNG file!";for(;r<n.length;){var g=a.readUint(n,r);r+=4;var u=a.readASCII(n,r,4);if(r+=4,"IHDR"==u)Q.decode._IHDR(n,r,s);else if("IDAT"==u){for(f=0;f<g;f++)l[c+f]=n[r+f];c+=g}else if("acTL"==u)s.tabs[u]={num_frames:o(n,r),num_plays:o(n,r+4)},t=new Uint8Array(n.length);else if("fcTL"==u){0!=d&&((k=s.frames[s.frames.length-1]).data=Q.decode._decompress(s,t.slice(0,d),k.rect.width,k.rect.height),d=0);var p={x:o(n,r+12),y:o(n,r+16),width:o(n,r+4),height:o(n,r+8)},b=i(n,r+22);b=i(n,r+20)/(0==b?100:b);var m={rect:p,delay:Math.round(1e3*b),dispose:n[r+24],blend:n[r+25]};s.frames.push(m)}else if("fdAT"==u){for(f=0;f<g-4;f++)t[d+f]=n[r+f+4];d+=g-4}else if("pHYs"==u)s.tabs[u]=[a.readUint(n,r),a.readUint(n,r+4),n[r+8]];else if("cHRM"==u)for(s.tabs[u]=[],f=0;f<8;f++)s.tabs[u].push(a.readUint(n,r+4*f));else if("tEXt"==u){null==s.tabs[u]&&(s.tabs[u]={});var w=a.nextZero(n,r),_=a.readASCII(n,r,w-r),v=a.readASCII(n,w+1,r+g-w-1);s.tabs[u][_]=v}else if("iTXt"==u){null==s.tabs[u]&&(s.tabs[u]={}),w=0;var y=r;w=a.nextZero(n,y),_=a.readASCII(n,y,w-y),n[y=w+1],n[y+1],y+=2,w=a.nextZero(n,y),a.readASCII(n,y,w-y),y=w+1,w=a.nextZero(n,y),a.readUTF8(n,y,w-y),y=w+1,v=a.readUTF8(n,y,g-(y-r)),s.tabs[u][_]=v}else if("PLTE"==u)s.tabs[u]=a.readBytes(n,r,g);else if("hIST"==u){var C=s.tabs.PLTE.length/3;for(s.tabs[u]=[],f=0;f<C;f++)s.tabs[u].push(i(n,r+2*f))}else if("tRNS"==u)3==s.ctype?s.tabs[u]=a.readBytes(n,r,g):0==s.ctype?s.tabs[u]=i(n,r):2==s.ctype&&(s.tabs[u]=[i(n,r),i(n,r+2),i(n,r+4)]);else if("gAMA"==u)s.tabs[u]=a.readUint(n,r)/1e5;else if("sRGB"==u)s.tabs[u]=n[r];else if("bKGD"==u)0==s.ctype||4==s.ctype?s.tabs[u]=[i(n,r)]:2==s.ctype||6==s.ctype?s.tabs[u]=[i(n,r),i(n,r+2),i(n,r+4)]:3==s.ctype&&(s.tabs[u]=n[r]);else if("IEND"==u){var k;0!=d&&((k=s.frames[s.frames.length-1]).data=Q.decode._decompress(s,t.slice(0,d),k.rect.width,k.rect.height),d=0),s.data=Q.decode._decompress(s,l,s.width,s.height);break}r+=g,a.readUint(n,r),r+=4}return delete s.compress,delete s.interlace,delete s.filter,s},Q.decode._decompress=function(e,t,n,r){return 0==e.compress&&(t=Q.decode._inflate(t)),0==e.interlace?t=Q.decode._filterZero(t,e,0,n,r):1==e.interlace&&(t=Q.decode._readInterlace(t,e)),t},Q.decode._inflate=function(e){return ee.inflate(e)},Q.decode._readInterlace=function(e,t){for(var n=t.width,r=t.height,a=Q.decode._getBPP(t),i=a>>3,o=Math.ceil(n*a/8),s=new Uint8Array(r*o),l=0,c=[0,0,4,0,2,0,1],d=[0,4,0,2,0,1,0],h=[8,8,8,4,4,2,2],f=[8,8,4,4,2,2,1],g=0;g<7;){for(var u=h[g],p=f[g],b=0,m=0,w=c[g];w<r;)w+=u,m++;for(var _=d[g];_<n;)_+=p,b++;var v=Math.ceil(b*a/8);Q.decode._filterZero(e,t,l,b,m);for(var y=0,C=c[g];C<r;){for(var k=d[g],x=l+y*v<<3;k<n;){var T;if(1==a&&(T=(T=e[x>>3])>>7-(7&x)&1,s[C*o+(k>>3)]|=T<<7-(3&k)),2==a&&(T=(T=e[x>>3])>>6-(7&x)&3,s[C*o+(k>>2)]|=T<<6-((3&k)<<1)),4==a&&(T=(T=e[x>>3])>>4-(7&x)&15,s[C*o+(k>>1)]|=T<<4-((1&k)<<2)),a>=8)for(var I=C*o+k*i,E=0;E<i;E++)s[I+E]=e[(x>>3)+E];x+=a,k+=p}y++,C+=u}b*m!=0&&(l+=m*(1+v)),g+=1}return s},Q.decode._getBPP=function(e){return[1,null,3,1,2,null,4][e.ctype]*e.depth},Q.decode._filterZero=function(e,t,n,r,a){var i=Q.decode._getBPP(t),o=Math.ceil(r*i/8),s=Q.decode._paeth;i=Math.ceil(i/8);for(var l=0;l<a;l++){var c=n+l*o,d=c+l+1,h=e[d-1];if(0==h)for(var f=0;f<o;f++)e[c+f]=e[d+f];else if(1==h){for(f=0;f<i;f++)e[c+f]=e[d+f];for(f=i;f<o;f++)e[c+f]=e[d+f]+e[c+f-i]&255}else if(0==l){for(f=0;f<i;f++)e[c+f]=e[d+f];if(2==h)for(f=i;f<o;f++)e[c+f]=255&e[d+f];if(3==h)for(f=i;f<o;f++)e[c+f]=e[d+f]+(e[c+f-i]>>1)&255;if(4==h)for(f=i;f<o;f++)e[c+f]=e[d+f]+s(e[c+f-i],0,0)&255}else{if(2==h)for(f=0;f<o;f++)e[c+f]=e[d+f]+e[c+f-o]&255;if(3==h){for(f=0;f<i;f++)e[c+f]=e[d+f]+(e[c+f-o]>>1)&255;for(f=i;f<o;f++)e[c+f]=e[d+f]+(e[c+f-o]+e[c+f-i]>>1)&255}if(4==h){for(f=0;f<i;f++)e[c+f]=e[d+f]+s(0,e[c+f-o],0)&255;for(f=i;f<o;f++)e[c+f]=e[d+f]+s(e[c+f-i],e[c+f-o],e[c+f-i-o])&255}}}return e},Q.decode._paeth=function(e,t,n){var r=e+t-n,a=Math.abs(r-e),i=Math.abs(r-t),o=Math.abs(r-n);return a<=i&&a<=o?e:i<=o?t:n},Q.decode._IHDR=function(e,t,n){var r=Q._bin;n.width=r.readUint(e,t),t+=4,n.height=r.readUint(e,t),t+=4,n.depth=e[t],t++,n.ctype=e[t],t++,n.compress=e[t],t++,n.filter=e[t],t++,n.interlace=e[t],t++},Q._bin={nextZero:function(e,t){for(;0!=e[t];)t++;return t},readUshort:function(e,t){return e[t]<<8|e[t+1]},writeUshort:function(e,t,n){e[t]=n>>8&255,e[t+1]=255&n},readUint:function(e,t){return 16777216*e[t]+(e[t+1]<<16|e[t+2]<<8|e[t+3])},writeUint:function(e,t,n){e[t]=n>>24&255,e[t+1]=n>>16&255,e[t+2]=n>>8&255,e[t+3]=255&n},readASCII:function(e,t,n){for(var r="",a=0;a<n;a++)r+=String.fromCharCode(e[t+a]);return r},writeASCII:function(e,t,n){for(var r=0;r<n.length;r++)e[t+r]=n.charCodeAt(r)},readBytes:function(e,t,n){for(var r=[],a=0;a<n;a++)r.push(e[t+a]);return r},pad:function(e){return e.length<2?"0"+e:e},readUTF8:function(e,t,n){for(var r,a="",i=0;i<n;i++)a+="%"+Q._bin.pad(e[t+i].toString(16));try{r=decodeURIComponent(a)}catch(o){return Q._bin.readASCII(e,t,n)}return r}},Q._copyTile=function(e,t,n,r,a,i,o,s,l){for(var c=Math.min(t,a),d=Math.min(n,i),h=0,f=0,g=0;g<d;g++)for(var u=0;u<c;u++)if(o>=0&&s>=0?(h=g*t+u<<2,f=(s+g)*a+o+u<<2):(h=(-s+g)*t-o+u<<2,f=g*a+u<<2),0==l)r[f]=e[h],r[f+1]=e[h+1],r[f+2]=e[h+2],r[f+3]=e[h+3];else if(1==l){var p=e[h+3]*(1/255),b=e[h]*p,m=e[h+1]*p,w=e[h+2]*p,_=r[f+3]*(1/255),v=r[f]*_,y=r[f+1]*_,C=r[f+2]*_,k=1-p,x=p+_*k,T=0==x?0:1/x;r[f+3]=255*x,r[f+0]=(b+v*k)*T,r[f+1]=(m+y*k)*T,r[f+2]=(w+C*k)*T}else if(2==l)p=e[h+3],b=e[h],m=e[h+1],w=e[h+2],_=r[f+3],v=r[f],y=r[f+1],C=r[f+2],p==_&&b==v&&m==y&&w==C?(r[f]=0,r[f+1]=0,r[f+2]=0,r[f+3]=0):(r[f]=b,r[f+1]=m,r[f+2]=w,r[f+3]=p);else if(3==l){if(p=e[h+3],b=e[h],m=e[h+1],w=e[h+2],_=r[f+3],v=r[f],y=r[f+1],C=r[f+2],p==_&&b==v&&m==y&&w==C)continue;if(p<220&&_>20)return!1}return!0},Q.encode=function(e,t,n,r,a,i){null==r&&(r=0),null==i&&(i=!1);for(var o=new Uint8Array(e[0].byteLength*e.length+100),s=[137,80,78,71,13,10,26,10],l=0;l<8;l++)o[l]=s[l];var c=8,d=Q._bin,h=Q.crc.crc,f=d.writeUint,g=d.writeUshort,u=d.writeASCII,p=Q.encode.compressPNG(e,t,n,r,i);f(o,c,13),u(o,c+=4,"IHDR"),f(o,c+=4,t),f(o,c+=4,n),o[c+=4]=p.depth,o[++c]=p.ctype,o[++c]=0,o[++c]=0,o[++c]=0,f(o,++c,h(o,c-17,17)),f(o,c+=4,1),u(o,c+=4,"sRGB"),o[c+=4]=1,f(o,++c,h(o,c-5,5)),c+=4;var b=e.length>1;if(b&&(f(o,c,8),u(o,c+=4,"acTL"),f(o,c+=4,e.length),f(o,c+=4,0),f(o,c+=4,h(o,c-12,12)),c+=4),3==p.ctype){for(f(o,c,3*(T=p.plte.length)),u(o,c+=4,"PLTE"),c+=4,l=0;l<T;l++){var m=3*l,w=p.plte[l],_=255&w,v=w>>8&255,y=w>>16&255;o[c+m+0]=_,o[c+m+1]=v,o[c+m+2]=y}if(f(o,c+=3*T,h(o,c-3*T-4,3*T+4)),c+=4,p.gotAlpha){for(f(o,c,T),u(o,c+=4,"tRNS"),c+=4,l=0;l<T;l++)o[c+l]=p.plte[l]>>24&255;f(o,c+=T,h(o,c-T-4,T+4)),c+=4}}for(var C=0,k=0;k<p.frames.length;k++){var x=p.frames[k];b&&(f(o,c,26),u(o,c+=4,"fcTL"),f(o,c+=4,C++),f(o,c+=4,x.rect.width),f(o,c+=4,x.rect.height),f(o,c+=4,x.rect.x),f(o,c+=4,x.rect.y),g(o,c+=4,a[k]),g(o,c+=2,1e3),o[c+=2]=x.dispose,o[++c]=x.blend,f(o,++c,h(o,c-30,30)),c+=4);var T,I=x.cimg;f(o,c,(T=I.length)+(0==k?0:4));var E=c+=4;for(u(o,c,0==k?"IDAT":"fdAT"),c+=4,0!=k&&(f(o,c,C++),c+=4),l=0;l<T;l++)o[c+l]=I[l];f(o,c+=T,h(o,E,c-E)),c+=4}return f(o,c,0),u(o,c+=4,"IEND"),f(o,c+=4,h(o,c-4,4)),c+=4,o.buffer.slice(0,c)},Q.encode.compressPNG=function(e,t,n,r,a){for(var i=Q.encode.compress(e,t,n,r,!1,a),o=0;o<e.length;o++){var s=i.frames[o];s.rect.width;var l=s.rect.height,c=s.bpl,d=s.bpp,h=new Uint8Array(l*c+l);s.cimg=Q.encode._filterZero(s.img,l,d,c,h)}return i},Q.encode.compress=function(e,t,n,r,a,i){null==i&&(i=!1);for(var o=6,s=8,l=4,c=255,d=0;d<e.length;d++)for(var h=new Uint8Array(e[d]),f=h.length,g=0;g<f;g+=4)c&=h[g+3];var u=255!=c,p={},b=[];if(0!=e.length&&(p[0]=0,b.push(0),0!=r&&r--),0!=r){var m=Q.quantize(e,r,a);for(e=m.bufs,g=0;g<m.plte.length;g++)null==p[_=m.plte[g].est.rgba]&&(p[_]=b.length,b.push(_))}else for(d=0;d<e.length;d++){var w=new Uint32Array(e[d]);for(f=w.length,g=0;g<f;g++){var _=w[g];if((g<t||_!=w[g-1]&&_!=w[g-t])&&null==p[_]&&(p[_]=b.length,b.push(_),b.length>=300))break}}var v=!!u&&a,y=b.length;y<=256&&0==i&&(s=y<=2?1:y<=4?2:y<=16?4:8,a&&(s=8),u=!0);var C=[];for(d=0;d<e.length;d++){var k=new Uint8Array(e[d]),x=new Uint32Array(k.buffer),T=0,I=0,E=t,S=n,z=0;if(0!=d&&!v){for(var P=a||1==d||2==C[C.length-2].dispose?1:2,R=0,D=1e9,A=0;A<P;A++){for(var $=new Uint8Array(e[d-1-A]),M=new Uint32Array(e[d-1-A]),U=t,B=n,N=-1,F=-1,O=0;O<n;O++)for(var W=0;W<t;W++)x[g=O*t+W]!=M[g]&&(W<U&&(U=W),W>N&&(N=W),O<B&&(B=O),O>F&&(F=O));var L=-1==N?1:(N-U+1)*(F-B+1);L<D&&(D=L,R=A,-1==N?(T=I=0,E=S=1):(T=U,I=B,E=N-U+1,S=F-B+1))}$=new Uint8Array(e[d-1-R]),1==R&&(C[C.length-1].dispose=2);var G=new Uint8Array(E*S*4);new Uint32Array(G.buffer),Q._copyTile($,t,n,G,E,S,-T,-I,0),Q._copyTile(k,t,n,G,E,S,-T,-I,3)?(Q._copyTile(k,t,n,G,E,S,-T,-I,2),z=1):(Q._copyTile(k,t,n,G,E,S,-T,-I,0),z=0),k=G,x=new Uint32Array(k.buffer)}var Z=4*E;if(y<=256&&0==i){for(Z=Math.ceil(s*E/8),G=new Uint8Array(Z*S),O=0;O<S;O++){g=O*Z;var H=O*E;if(8==s)for(W=0;W<E;W++)G[g+W]=p[x[H+W]];else if(4==s)for(W=0;W<E;W++)G[g+(W>>1)]|=p[x[H+W]]<<4-4*(1&W);else if(2==s)for(W=0;W<E;W++)G[g+(W>>2)]|=p[x[H+W]]<<6-2*(3&W);else if(1==s)for(W=0;W<E;W++)G[g+(W>>3)]|=p[x[H+W]]<<7-1*(7&W)}k=G,o=3,l=1}else if(0==u&&1==e.length){G=new Uint8Array(E*S*3);var j=E*S;for(g=0;g<j;g++){var q=3*g,K=4*g;G[q]=k[K],G[q+1]=k[K+1],G[q+2]=k[K+2]}k=G,o=2,l=3,Z=3*E}C.push({rect:{x:T,y:I,width:E,height:S},img:k,bpl:Z,bpp:l,blend:z,dispose:v?1:0})}return{ctype:o,depth:s,plte:b,gotAlpha:u,frames:C}},Q.encode._filterZero=function(e,t,n,r,a){for(var i=[],o=0;o<5;o++)if(!(t*r>5e5)||2!=o&&3!=o&&4!=o){for(var s=0;s<t;s++)Q.encode._filterLine(a,e,s,r,n,o);if(i.push(ee.deflate(a)),1==n)break}for(var l,c=1e9,d=0;d<i.length;d++)i[d].length<c&&(l=d,c=i[d].length);return i[l]},Q.encode._filterLine=function(e,t,n,r,a,i){var o=n*r,s=o+n,l=Q.decode._paeth;if(e[s]=i,s++,0==i)for(var c=0;c<r;c++)e[s+c]=t[o+c];else if(1==i){for(c=0;c<a;c++)e[s+c]=t[o+c];for(c=a;c<r;c++)e[s+c]=t[o+c]-t[o+c-a]+256&255}else if(0==n){for(c=0;c<a;c++)e[s+c]=t[o+c];if(2==i)for(c=a;c<r;c++)e[s+c]=t[o+c];if(3==i)for(c=a;c<r;c++)e[s+c]=t[o+c]-(t[o+c-a]>>1)+256&255;if(4==i)for(c=a;c<r;c++)e[s+c]=t[o+c]-l(t[o+c-a],0,0)+256&255}else{if(2==i)for(c=0;c<r;c++)e[s+c]=t[o+c]+256-t[o+c-r]&255;if(3==i){for(c=0;c<a;c++)e[s+c]=t[o+c]+256-(t[o+c-r]>>1)&255;for(c=a;c<r;c++)e[s+c]=t[o+c]+256-(t[o+c-r]+t[o+c-a]>>1)&255}if(4==i){for(c=0;c<a;c++)e[s+c]=t[o+c]+256-l(0,t[o+c-r],0)&255;for(c=a;c<r;c++)e[s+c]=t[o+c]+256-l(t[o+c-a],t[o+c-r],t[o+c-a-r])&255}}},Q.crc={table:function(){for(var e=new Uint32Array(256),t=0;t<256;t++){for(var n=t,r=0;r<8;r++)1&n?n=3988292384^n>>>1:n>>>=1;e[t]=n}return e}(),update:function(e,t,n,r){for(var a=0;a<r;a++)e=Q.crc.table[255&(e^t[n+a])]^e>>>8;return e},crc:function(e,t,n){return 4294967295^Q.crc.update(4294967295,e,t,n)}},Q.quantize=function(e,t,n){for(var r=[],a=0,i=0;i<e.length;i++)r.push(Q.encode.alphaMul(new Uint8Array(e[i]),n)),a+=e[i].byteLength;var o=new Uint8Array(a),s=new Uint32Array(o.buffer),l=0;for(i=0;i<r.length;i++){for(var c=r[i],d=c.length,h=0;h<d;h++)o[l+h]=c[h];l+=d}var f={i0:0,i1:o.length,bst:null,est:null,tdst:0,left:null,right:null};f.bst=Q.quantize.stats(o,f.i0,f.i1),f.est=Q.quantize.estats(f.bst);for(var g=[f];g.length<t;){var u=0,p=0;for(i=0;i<g.length;i++)g[i].est.L>u&&(u=g[i].est.L,p=i);if(u<.001)break;var b=g[p],m=Q.quantize.splitPixels(o,s,b.i0,b.i1,b.est.e,b.est.eMq255),w={i0:b.i0,i1:m,bst:null,est:null,tdst:0,left:null,right:null};w.bst=Q.quantize.stats(o,w.i0,w.i1),w.est=Q.quantize.estats(w.bst);var _={i0:m,i1:b.i1,bst:null,est:null,tdst:0,left:null,right:null};for(_.bst={R:[],m:[],N:b.bst.N-w.bst.N},i=0;i<16;i++)_.bst.R[i]=b.bst.R[i]-w.bst.R[i];for(i=0;i<4;i++)_.bst.m[i]=b.bst.m[i]-w.bst.m[i];_.est=Q.quantize.estats(_.bst),b.left=w,b.right=_,g[p]=w,g.push(_)}g.sort(function(e,t){return t.bst.N-e.bst.N});for(var v=0;v<r.length;v++){var y=Q.quantize.planeDst,C=new Uint8Array(r[v].buffer),k=new Uint32Array(r[v].buffer),x=C.length;for(i=0;i<x;i+=4){for(var T=C[i]*(1/255),I=C[i+1]*(1/255),E=C[i+2]*(1/255),S=C[i+3]*(1/255),z=f;z.left;)z=y(z.est,T,I,E,S)<=0?z.left:z.right;k[i>>2]=z.est.rgba}r[v]=k.buffer}return{bufs:r,plte:g}},Q.quantize.getNearest=function(e,t,n,r,a){if(null==e.left)return e.tdst=Q.quantize.dist(e.est.q,t,n,r,a),e;var i=Q.quantize.planeDst(e.est,t,n,r,a),o=e.left,s=e.right;i>0&&(o=e.right,s=e.left);var l=Q.quantize.getNearest(o,t,n,r,a);if(l.tdst<=i*i)return l;var c=Q.quantize.getNearest(s,t,n,r,a);return c.tdst<l.tdst?c:l},Q.quantize.planeDst=function(e,t,n,r,a){var i=e.e;return i[0]*t+i[1]*n+i[2]*r+i[3]*a-e.eMq},Q.quantize.dist=function(e,t,n,r,a){var i=t-e[0],o=n-e[1],s=r-e[2],l=a-e[3];return i*i+o*o+s*s+l*l},Q.quantize.splitPixels=function(e,t,n,r,a,i){var o=Q.quantize.vecDot;for(r-=4;n<r;){for(;o(e,n,a)<=i;)n+=4;for(;o(e,r,a)>i;)r-=4;if(n>=r)break;var s=t[n>>2];t[n>>2]=t[r>>2],t[r>>2]=s,n+=4,r-=4}for(;o(e,n,a)>i;)n-=4;return n+4},Q.quantize.vecDot=function(e,t,n){return e[t]*n[0]+e[t+1]*n[1]+e[t+2]*n[2]+e[t+3]*n[3]},Q.quantize.stats=function(e,t,n){for(var r=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],a=[0,0,0,0],i=n-t>>2,o=t;o<n;o+=4){var s=e[o]*(1/255),l=e[o+1]*(1/255),c=e[o+2]*(1/255),d=e[o+3]*(1/255);a[0]+=s,a[1]+=l,a[2]+=c,a[3]+=d,r[0]+=s*s,r[1]+=s*l,r[2]+=s*c,r[3]+=s*d,r[5]+=l*l,r[6]+=l*c,r[7]+=l*d,r[10]+=c*c,r[11]+=c*d,r[15]+=d*d}return r[4]=r[1],r[8]=r[2],r[12]=r[3],r[9]=r[6],r[13]=r[7],r[14]=r[11],{R:r,m:a,N:i}},Q.quantize.estats=function(e){var t=e.R,n=e.m,r=e.N,a=n[0],i=n[1],o=n[2],s=n[3],l=0==r?0:1/r,c=[t[0]-a*a*l,t[1]-a*i*l,t[2]-a*o*l,t[3]-a*s*l,t[4]-i*a*l,t[5]-i*i*l,t[6]-i*o*l,t[7]-i*s*l,t[8]-o*a*l,t[9]-o*i*l,t[10]-o*o*l,t[11]-o*s*l,t[12]-s*a*l,t[13]-s*i*l,t[14]-s*o*l,t[15]-s*s*l],d=c,h=Q.M4,f=[.5,.5,.5,.5],g=0,u=0;if(0!=r)for(var p=0;p<10&&(f=h.multVec(d,f),u=Math.sqrt(h.dot(f,f)),f=h.sml(1/u,f),!(Math.abs(u-g)<1e-9));p++)g=u;var b=[a*l,i*l,o*l,s*l],m=h.dot(h.sml(255,b),f),w=b[3]<.001?0:1/b[3];return{Cov:c,q:b,e:f,L:g,eMq255:m,eMq:h.dot(f,b),rgba:(Math.round(255*b[3])<<24|Math.round(255*b[2]*w)<<16|Math.round(255*b[1]*w)<<8|Math.round(255*b[0]*w))>>>0}},Q.M4={multVec:function(e,t){return[e[0]*t[0]+e[1]*t[1]+e[2]*t[2]+e[3]*t[3],e[4]*t[0]+e[5]*t[1]+e[6]*t[2]+e[7]*t[3],e[8]*t[0]+e[9]*t[1]+e[10]*t[2]+e[11]*t[3],e[12]*t[0]+e[13]*t[1]+e[14]*t[2]+e[15]*t[3]]},dot:function(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]+e[3]*t[3]},sml:function(e,t){return[e*t[0],e*t[1],e*t[2],e*t[3]]}},Q.encode.alphaMul=function(e,t){for(var n=new Uint8Array(e.length),r=e.length>>2,a=0;a<r;a++){var i=a<<2,o=e[i+3];t&&(o=o<128?0:255);var s=o*(1/255);n[i+0]=e[i+0]*s,n[i+1]=e[i+1]*s,n[i+2]=e[i+2]*s,n[i+3]=o}return n});class le extends Error{constructor(e,t,n){super(`CDP operation '${e}' timed out after ${t}ms`),this.operation=e,this.timeoutMs=t,this.context=n,this.name="CDPTimeoutError"}}var ce,de,he,fe,ge,ue,pe,be,me,we,_e,ve,ye,Ce=Object.defineProperty,ke=e=>{throw TypeError(e)},xe=(e,t,n)=>((e,t,n)=>t in e?Ce(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n)(e,"symbol"!=typeof t?t+"":t,n),Te=(e,t,n)=>t.has(e)||ke("Cannot "+n),Ie=(e,t,n)=>(Te(e,t,"read from private field"),n?n.call(e):t.get(e)),Ee=(e,t,n)=>t.has(e)?ke("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),Se=(e,t,n,r)=>(Te(e,t,"write to private field"),t.set(e,n),n),ze=(e,t,n)=>(Te(e,t,"access private method"),n);const Pe=void 0,Re="ConnectionRefused",De="ConnectionError",Ae="GET",$e={apiUrl:"http://localhost:9222",apiPath:"json/version",apiPathTargets:"json",apiPathNewTarget:"json/new",apiPathActivateTarget:"json/activate",apiPathCloseTarget:"json/close",connectionMaxRetry:20,connectionRetryDelay:500};class Me{constructor(e){Ee(this,ce),Ee(this,de,Object.assign({},Ue)),Ee(this,he,new Map),xe(this,"Runtime"),xe(this,"Target"),xe(this,"Page"),xe(this,"Input"),xe(this,"Network"),xe(this,"DOM"),xe(this,"Emulation"),xe(this,"Debugger"),xe(this,"Console"),xe(this,"CSS"),xe(this,"Profiler"),xe(this,"HeapProfiler"),xe(this,"Security"),xe(this,"ServiceWorker"),xe(this,"Storage"),xe(this,"SystemInfo"),xe(this,"Browser"),xe(this,"Animation"),xe(this,"Accessibility"),Object.assign(Ie(this,de),e);return["Runtime","Target","Page","Console","Network","Input","DOM","CSS","Debugger","Profiler","HeapProfiler","Security","ServiceWorker","Storage","SystemInfo","Browser","Emulation","Animation","Accessibility"].forEach(e=>{this[e]=this.createDomain(e)}),new Proxy(this,{get:(e,t)=>"string"==typeof t?(t in e||(e[t]=e.createDomain(t)),e[t]):e[t]})}createDomain(e){const t=this;return new Proxy(Object.create(null),{get(n,r){if("string"==typeof r)return"addEventListener"===r?t.getDomainListenerFunction("addEventListener",e):"removeEventListener"===r?t.getDomainListenerFunction("removeEventListener",e):(n[r]||(n[r]=t.getDomainMethodFunction(r,e)),n[r])}})}getDomainMethodFunction(e,t){const n=this;return async(r={},a)=>{await n.ready();const i=Ie(n,he).get(t);if(i!==Pe){for(;i.length>0;){const e=i.shift();e&&Ie(n,ce)&&Ie(n,ce)[e.methodName](`${e.domainName}.${e.type}`,e.listener)}Ie(n,he).delete(t)}if(!Ie(n,ce))throw new Error("Connection not established");const o=r||{};return Ie(n,ce).sendMessage(`${t}.${e}`,o,a)}}getDomainListenerFunction(e,t){const n=this;return(r,a)=>{if(Ie(n,ce)===Pe){let i=Ie(n,he).get(t);i===Pe&&(i=[],Ie(n,he).set(t,i)),i.push({methodName:e,domainName:t,type:r,listener:a})}else Ie(n,ce)[e](`${t}.${r}`,a)}}async ready(){if(Ie(this,ce)===Pe){console.log("[kazeel:simple-cdp] CDP: Establishing connection...");let e=Ie(this,de).webSocketDebuggerUrl;if(e===Pe){console.log("[kazeel:simple-cdp] CDP: No WebSocket URL provided, fetching from API...");const t=new URL(Ie(this,de).apiPath,Ie(this,de).apiUrl);e=(await Ne(t,Ie(this,de))).webSocketDebuggerUrl,console.log(`[kazeel:simple-cdp] CDP: Retrieved WebSocket URL: ${e}`)}const t=new Be(e);await t.open(),Se(this,ce,t),console.log("[kazeel:simple-cdp] CDP: Connection established successfully")}}get options(){return Ie(this,de)}set options(e){Object.assign(Ie(this,de),e)}get connection(){if(!Ie(this,ce))throw new Error("Connection not established. Call a CDP method first to establish connection.");return Ie(this,ce)}reset(){Ie(this,ce)!==Pe?(console.log("[kazeel:simple-cdp] CDP: Resetting connection and clearing pending event listeners"),Ie(this,ce).close(),Se(this,ce,Pe),Ie(this,he).clear(),console.log("[kazeel:simple-cdp] CDP: Reset completed")):console.log("[kazeel:simple-cdp] CDP: Reset called but no connection exists")}static getTargets(){const{apiPathTargets:e,apiUrl:t}=Ue;return Ne(new URL(e,t),Ue)}static createTarget(e){const{apiPathNewTarget:t,apiUrl:n}=Ue;return Ne(new URL(e?`${t}?${e}`:t,n),Ue,"PUT")}static async activateTarget(e){const{apiPathActivateTarget:t,apiUrl:n}=Ue;await Ne(new URL(`${t}/${e}`,n),Ue,Ae,!1)}static async closeTarget(e){const{apiPathCloseTarget:t,apiUrl:n}=Ue;await Ne(new URL(`${t}/${e}`,n),Ue,Ae,!1)}}ce=new WeakMap,de=new WeakMap,he=new WeakMap;const Ue=Object.assign({},$e);new Me(Ue);class Be extends EventTarget{constructor(e){super(),Ee(this,we),Ee(this,fe),Ee(this,ge),Ee(this,ue,new Map),Ee(this,pe,0),Ee(this,be),Ee(this,me,4e4),Se(this,fe,e)}open(){return console.log(`[kazeel:simple-cdp] CDP WebSocket: Attempting to connect to ${Ie(this,fe)}`),Se(this,ge,new WebSocket(Ie(this,fe))),Ie(this,ge).addEventListener("message",e=>{ze(this,we,ye).call(this,JSON.parse(e.data))}),new Promise((e,t)=>{Ie(this,ge).addEventListener("open",()=>{console.log("[kazeel:simple-cdp] CDP WebSocket: Connection opened successfully"),ze(this,we,_e).call(this),e()}),Ie(this,ge).addEventListener("close",e=>{console.log(`[kazeel:simple-cdp] CDP WebSocket: Connection closed - Code: ${e.code}, Reason: ${e.reason}, WasClean: ${e.wasClean}`),ze(this,we,ve).call(this),t(new Error(e.reason))}),Ie(this,ge).addEventListener("error",e=>{console.log("[kazeel:simple-cdp] CDP WebSocket: Error occurred",e),t(new Error("WebSocket error"))})})}sendMessage(e,t={},n){if(!Ie(this,ge))throw new Error("WebSocket not connected");const r=Ie(this,pe),a=JSON.stringify({id:r,method:e,params:t,sessionId:n});let i;Se(this,pe,(Ie(this,pe)+1)%Number.MAX_SAFE_INTEGER),Ie(this,ge).send(a);const o=new Promise((r,a)=>i={resolve:r,reject:a,method:e,params:t,sessionId:n});return Ie(this,ue).set(r,i),function(e,t,n=3e4,r){let a;const i=new Promise((e,i)=>{a=setTimeout(()=>{i(new le(t,n,r))},n)});return Promise.race([e.finally(()=>{a&&clearTimeout(a)}),i])}(o,e,1e4,{sessionId:n,params:Object.keys(t).length>0?t:void 0})}close(){ze(this,we,ve).call(this),Ie(this,ge)&&Ie(this,ge).close()}}function Ne(e,t,n=Ae,r=!0){return Fe(async()=>{let t;try{t=await fetch(e,{method:n})}catch(a){const e=a;throw e.code=Re,e}if(t.status>=400){const e=new Error(t.statusText||`HTTP Error ${t.status}`);throw e.status=t.status,e.code=De,e}return r?t.json():t.text()},t)}async function Fe(e,t,n=0){const{connectionMaxRetry:r,connectionRetryDelay:a}=t;try{return await e()}catch(i){if(i.code===Re&&n<r)return await new Promise(e=>setTimeout(e,a)),Fe(e,t,n+1);throw i}}fe=new WeakMap,ge=new WeakMap,ue=new WeakMap,pe=new WeakMap,be=new WeakMap,me=new WeakMap,we=new WeakSet,_e=function(){console.log(`[kazeel:simple-cdp] CDP WebSocket: Starting keep-alive with ${Ie(this,me)}ms interval`),Se(this,be,setInterval(()=>{Ie(this,ge)&&Ie(this,ge).readyState===WebSocket.OPEN&&this.sendMessage("Browser.getVersion").catch(e=>{console.log("[kazeel:simple-cdp] CDP WebSocket: Keep-alive failed:",e)})},Ie(this,me)))},ve=function(){Ie(this,be)&&(console.log("[kazeel:simple-cdp] CDP WebSocket: Stopping keep-alive"),clearInterval(Ie(this,be)),Se(this,be,void 0))},ye=function({id:e,method:t,result:n,error:r,params:a,sessionId:i}){if(e!==Pe){const t=Ie(this,ue).get(e);if(t){const{resolve:a,reject:i}=t;if(r===Pe)a(n);else{const e=r.message+` when calling ${t.method}(${JSON.stringify(t.params)})${t.sessionId===Pe?"":` (sessionId ${JSON.stringify(t.sessionId)})`}`,n=new Error(e);n.code=r.code,i(n)}Ie(this,ue).delete(e)}}if(t!==Pe){const e=new Event(t);e.params=a,e.sessionId=i,this.dispatchEvent(e)}};const Oe=Object.freeze(Object.defineProperty({__proto__:null,CDP:Me,CONNECTION_ERROR_CODE:De,CONNECTION_REFUSED_ERROR_CODE:Re,options:Ue},Symbol.toStringTag,{value:"Module"}))}();
