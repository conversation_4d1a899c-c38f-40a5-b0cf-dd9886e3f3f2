/**
 * WebSocket Signaling Server for POC Streaming
 *
 * Handles WebRTC signaling between target tabs, control tab, and web clients
 * Manages connection state and provides reconnection logic
 */

import WebSocket, { WebSocketServer } from "ws";

// Simple UUID generator
function generateId() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

export class SignalingServer {
  constructor(options = {}) {
    this.options = {
      port: options.port || 8080,
      ...options,
    };

    this.wss = null;
    this.clients = new Map(); // clientId -> { ws, type, metadata }
    this.targetTabs = new Map(); // tabId -> clientId
    this.controlTab = null; // clientId of control tab
    this.webClients = new Set(); // Set of web client IDs
    this.scriptInjector = options.scriptInjector || null; // Script injector for removing scripts

    // Single-stream state - now using tabId as primary identifier
    this.currentStream = null; // { tabId, webClientId }
    this.availableTabs = new Map(); // tabId -> { title, url, clientId }

    this.isRunning = false;
  }

  /**
   * Start the signaling server
   */
  async start() {
    if (this.isRunning) {
      console.log("Signaling server already running");
      return;
    }

    console.log(`🚀 Starting signaling server on port ${this.options.port}...`);

    this.wss = new WebSocketServer({
      port: this.options.port,
      perMessageDeflate: false,
    });

    this.wss.on("connection", (ws, request) => {
      this.handleConnection(ws, request);
    });

    this.wss.on("error", (error) => {
      console.error("WebSocket server error:", error);
    });

    this.isRunning = true;
    console.log(
      `✅ Signaling server started on ws://localhost:${this.options.port}`
    );
  }

  /**
   * Handle new WebSocket connection
   */
  handleConnection(ws, request) {
    const clientId = generateId();
    const clientInfo = {
      id: clientId,
      ws,
      type: null, // 'target-tab', 'control-tab', 'web-client'
      metadata: {},
      connected: true,
      lastActivity: Date.now(),
    };

    this.clients.set(clientId, clientInfo);
    console.log(`📱 Client connected: ${clientId}`);

    // Send welcome message
    this.sendToClient(clientId, {
      type: "welcome",
      clientId,
      timestamp: Date.now(),
    });

    // Setup message handling
    ws.on("message", async (data) => {
      try {
        const message = JSON.parse(data.toString());
        await this.handleMessage(clientId, message);
      } catch (error) {
        console.error(`Invalid JSON from client ${clientId}:`, error);
        this.sendToClient(clientId, {
          type: "error",
          message: "Invalid JSON format",
        });
      }
    });

    // Handle disconnection
    ws.on("close", () => {
      // this.handleDisconnection(clientId).catch(console.error);
    });

    ws.on("error", (error) => {
      console.error(`Client ${clientId} error:`, error);
      this.handleDisconnection(clientId).catch(console.error);
    });
  }

  /**
   * Handle incoming messages from clients
   */
  async handleMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) {
      console.warn(`Message from unknown client: ${clientId}`);
      return;
    }

    client.lastActivity = Date.now();
    console.log(
      `📨 Message from ${clientId} (${client.type}): ${message.type}`
    );

    switch (message.type) {
      case "register-target-tab":
        this.handleRegisterTargetTab(clientId, message);
        break;
      case "register-control-tab":
        this.handleRegisterControlTab(clientId, message);
        break;
      case "register-web-client":
        await this.handleRegisterWebClient(clientId, message);
        break;
      case "request-stream":
        this.handleRequestStream(clientId, message);
        break;
      case "stop-stream":
        this.handleStopStream(clientId, message);
        break;
      case "webrtc-offer":
        this.handleWebRTCOffer(clientId, message);
        break;
      case "webrtc-answer":
        this.handleWebRTCAnswer(clientId, message);
        break;
      case "webrtc-ice-candidate":
        this.handleWebRTCIceCandidate(clientId, message);
        break;
      case "webrtc-offer-from-target":
        this.handleWebRTCOfferFromTarget(clientId, message);
        break;
      case "webrtc-ice-candidate-from-target":
        this.handleWebRTCIceCandidateFromTarget(clientId, message);
        break;
      case "webrtc-answer-to-target":
        this.handleWebRTCAnswerToTarget(clientId, message);
        break;
      case "webrtc-ice-candidate-to-target":
        this.handleWebRTCIceCandidateToTarget(clientId, message);
        break;
      case "webrtc-offer-to-web-client":
        this.handleWebRTCOfferToWebClient(clientId, message);
        break;
      case "webrtc-ice-candidate-to-web-client":
        this.handleWebRTCIceCandidateToWebClient(clientId, message);
        break;
      case "webrtc-answer-from-web-client":
        this.handleWebRTCAnswerFromWebClient(clientId, message);
        break;
      case "webrtc-ice-candidate-from-web-client":
        this.handleWebRTCIceCandidateFromWebClient(clientId, message);
        break;
      case "streaming-ready":
        this.handleStreamingReady(clientId, message);
        break;
      case "request-interceptor-status":
        this.handleRequestInterceptorStatus(clientId, message);
        break;
      case "interceptor-status-to-web-client":
        this.handleInterceptorStatusToWebClient(clientId, message);
        break;
      case "configure-interceptor-pipeline":
        this.handleConfigureInterceptorPipeline(clientId, message);
        break;
      case "request-pipeline-status":
        this.handleRequestPipelineStatus(clientId, message);
        break;
      case "pipeline-status-to-web-client":
        this.handlePipelineStatusToWebClient(clientId, message);
        break;
      case "stream-paused":
        this.handleStreamPausedNotification(clientId, message);
        break;
      case "stream-resumed":
        this.handleStreamResumedNotification(clientId, message);
        break;
      default:
        console.warn(`Unknown message type from ${clientId}: ${message.type}`);
    }
  }

  handleConfigureInterceptorPipeline(clientId, message) {
    console.log(
      `🎛️ Configure pipeline request from ${clientId} for tab: ${message.tabId}`
    );

    // Forward request to control tab
    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "configure-interceptor-pipeline",
        tabId: message.tabId,
        interceptorNames: message.interceptorNames,
        interceptorConfigs: message.interceptorConfigs,
        webClientId: clientId,
      });
    } else {
      this.sendToClient(clientId, {
        type: "error",
        message: "No control tab available to handle interceptor commands",
      });
    }
  }

  handleRequestPipelineStatus(clientId, message) {
    console.log(
      `🎛️ Pipeline status request from ${clientId} for tab: ${message.tabId}`
    );

    // Forward request to control tab if available
    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "request-pipeline-status",
        tabId: message.tabId,
        webClientId: clientId,
      });
    } else {
      // No control tab available
      this.sendToClient(clientId, {
        type: "pipeline-status-to-web-client",
        interceptorNames: [],
        interceptorConfigs: [],
        error: "No control tab available",
      });
    }
  }

  handlePipelineStatusToWebClient(clientId, message) {
    console.log(
      `🎛️ Pipeline status response from control tab to web client: ${message.webClientId}`
    );

    // Forward status to web client
    if (message.webClientId && this.clients.has(message.webClientId)) {
      this.sendToClient(message.webClientId, {
        type: "pipeline-status-to-web-client",
        interceptorNames: message.interceptorNames,
        interceptorConfigs: message.interceptorConfigs,
      });
    } else {
      console.warn(
        `Web client ${message.webClientId} not found for pipeline status`
      );
    }
  }

  /**
   * Handle stream paused notification from control tab to web client
   */
  handleStreamPausedNotification(clientId, message) {
    console.log(
      `🔴 Stream paused notification from control tab to web client: ${message.webClientId}`
    );

    // Forward notification to web client
    if (message.webClientId && this.clients.has(message.webClientId)) {
      this.sendToClient(message.webClientId, {
        type: "stream-paused",
        reason: message.reason,
        changeThreshold: message.changeThreshold,
        stabilityThreshold: message.stabilityThreshold,
        consecutiveStableFramesRequired:
          message.consecutiveStableFramesRequired,
        timestamp: message.timestamp,
      });
    } else {
      console.warn(
        `Web client ${message.webClientId} not found for stream paused notification`
      );
    }
  }

  /**
   * Handle stream resumed notification from control tab to web client
   */
  handleStreamResumedNotification(clientId, message) {
    console.log(
      `🟢 Stream resumed notification from control tab to web client: ${message.webClientId}`
    );

    // Forward notification to web client
    if (message.webClientId && this.clients.has(message.webClientId)) {
      this.sendToClient(message.webClientId, {
        type: "stream-resumed",
        reason: message.reason,
        pauseDuration: message.pauseDuration,
        consecutiveStableFramesAchieved:
          message.consecutiveStableFramesAchieved,
        maxWaitDuration: message.maxWaitDuration,
        timestamp: message.timestamp,
      });
    } else {
      console.warn(
        `Web client ${message.webClientId} not found for stream resumed notification`
      );
    }
  }

  /**
   * Register a target tab
   */
  handleRegisterTargetTab(clientId, message) {
    const client = this.clients.get(clientId);
    client.type = "target-tab";
    client.metadata = {
      tabId: message.tabId,
      url: message.url,
      title: message.title,
    };

    this.targetTabs.set(message.tabId, clientId);

    // Store in available tabs for single-stream mode
    this.availableTabs.set(message.tabId, {
      title: message.title,
      url: message.url,
      clientId: clientId,
    });

    console.log(`📑 Target tab registered: ${message.title} (${message.url})`);

    // Notify web clients about available tabs
    this.broadcastToType("web-client", {
      type: "available-tabs-updated",
      tabs: Array.from(this.availableTabs.entries()).map(([tabId, info]) => ({
        tabId,
        title: info.title,
        url: info.url,
      })),
    });
  }

  /**
   * Register the control tab
   */
  handleRegisterControlTab(clientId, message) {
    const client = this.clients.get(clientId);
    client.type = "control-tab";
    client.metadata = message.metadata || {};

    this.controlTab = clientId;

    console.log(`📋 Control tab registered: ${clientId}`);

    // Send current target tabs to control tab
    const targetTabs = Array.from(this.targetTabs.entries()).map(
      ([tabId, targetClientId]) => {
        const targetClient = this.clients.get(targetClientId);
        return {
          tabId,
          url: targetClient?.metadata?.url,
          title: targetClient?.metadata?.title,
          clientId: targetClientId,
        };
      }
    );

    this.sendToClient(clientId, {
      type: "target-tabs-list",
      targetTabs,
    });
  }

  /**
   * Register a web client
   */
  async handleRegisterWebClient(clientId, message) {
    const client = this.clients.get(clientId);
    client.type = "web-client";
    client.metadata = message.metadata || {};
    client.purpose = message.purpose || "screen-stream"; // Default to screen-stream

    this.webClients.add(clientId);

    console.log(
      `🌐 Web client registered: ${clientId} with purpose: ${client.purpose}`
    );

    // Notify control tab about new web client registration
    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "web-client-registered",
        webClientId: clientId,
        purpose: client.purpose,
        metadata: message.metadata || {},
      });
    }

    // Handle dynamic bundle injection based on client purposes
    if (this.scriptInjector) {
      const allPurposes = this.getConnectedClientPurposes();
      await this.scriptInjector.handleClientPurposeChange(allPurposes);
    }

    // If this is a captcha client, notify control tab to configure interceptors
    if (client.purpose === "captcha" && this.controlTab) {
      console.log(
        `🔍 Notifying control tab to configure captcha interceptors for client: ${clientId}`
      );
      this.sendToClient(this.controlTab, {
        type: "configure-client-interceptors",
        webClientId: clientId,
        purpose: client.purpose,
      });
    }

    // Send available target tabs to web client
    const targetTabs = Array.from(this.targetTabs.entries()).map(
      ([tabId, targetClientId]) => {
        const targetClient = this.clients.get(targetClientId);
        return {
          tabId,
          url: targetClient?.metadata?.url,
          title: targetClient?.metadata?.title,
          isStreaming: this.isTabStreaming(tabId),
        };
      }
    );

    this.sendToClient(clientId, {
      type: "available-streams",
      targetTabs,
    });
  }

  /**
   * Get all connected client purposes for script injection decisions
   */
  getConnectedClientPurposes() {
    const purposes = [];
    for (const [clientId, client] of this.clients) {
      if (client.type === "web-client" && client.purpose) {
        purposes.push(client.purpose);
      }
    }
    return [...new Set(purposes)]; // Return unique purposes
  }

  /**
   * Handle single-stream request from web client
   */
  async handleRequestStream(clientId, message) {
    const tabId = message.tabId;
    console.log(`🎬 Single-stream requested for tab: ${tabId}`);

    // Check if tab exists
    if (!this.availableTabs.has(tabId)) {
      this.sendToClient(clientId, {
        type: "error",
        message: `Tab not found: ${tabId}`,
      });
      return;
    }

    // Stop current stream if any
    if (this.currentStream) {
      await this.stopCurrentStream();
    }

    // Start new stream
    this.startSingleStream(tabId, clientId);
  }

  /**
   * Start a single stream
   */
  startSingleStream(tabId, webClientId) {
    const targetClientId = this.targetTabs.get(tabId);

    if (!targetClientId) {
      console.error(`Target client not found for tab: ${tabId}`);
      return;
    }

    // Set current stream - using tabId as primary identifier
    this.currentStream = {
      tabId,
      webClientId,
    };

    // Tell target tab to start streaming
    this.sendToClient(targetClientId, {
      type: "start-streaming",
      tabId,
    });

    console.log(`🎥 Single stream started for tab: ${tabId}`);
  }

  /**
   * Stop current stream
   */
  async stopCurrentStream() {
    if (!this.currentStream) return;

    const tabId = this.currentStream.tabId;
    console.log(`🛑 Stopping current stream for tab: ${tabId}`);

    // Notify target tab to stop
    const targetClientId = this.targetTabs.get(tabId);
    if (targetClientId) {
      this.sendToClient(targetClientId, {
        type: "stop-streaming",
        tabId: tabId,
      });
    }

    // Remove the injected script from the tab
    if (this.scriptInjector) {
      try {
        await this.scriptInjector.removeScript(tabId);
        console.log(`🗑️ Script removed from tab: ${tabId}`);
      } catch (error) {
        console.error(`❌ Failed to remove script from tab ${tabId}:`, error);
      }
    }

    this.currentStream = null;
  }

  /**
   * Handle stop stream request
   */
  async handleStopStream(clientId, message) {
    const tabId = message.tabId;

    if (!this.currentStream || this.currentStream.tabId !== tabId) {
      console.log(`❌ No active stream for tab: ${tabId}`);
      this.sendToClient(clientId, {
        type: "error",
        message: `No active stream for tab: ${tabId}`,
      });
      return;
    }

    console.log(`🛑 Stopping stream for tab: ${tabId}`);
    await this.stopCurrentStream();
  }

  /**
   * Handle WebRTC offer
   */
  handleWebRTCOffer(clientId, message) {
    console.log(
      `🤝 WebRTC offer from ${clientId} to ${message.targetClientId}`
    );

    if (message.targetClientId) {
      this.sendToClient(message.targetClientId, {
        type: "webrtc-offer",
        offer: message.offer,
        fromClientId: clientId,
      });
    }
  }

  /**
   * Handle WebRTC answer
   */
  handleWebRTCAnswer(clientId, message) {
    console.log(
      `🤝 WebRTC answer from ${clientId} to ${message.targetClientId}`
    );

    if (message.targetClientId) {
      this.sendToClient(message.targetClientId, {
        type: "webrtc-answer",
        answer: message.answer,
        fromClientId: clientId,
      });
    }
  }

  /**
   * Handle WebRTC ICE candidate
   */
  handleWebRTCIceCandidate(clientId, message) {
    console.log(
      `🧊 ICE candidate from ${clientId} to ${message.targetClientId}`
    );

    if (message.targetClientId) {
      this.sendToClient(message.targetClientId, {
        type: "webrtc-ice-candidate",
        candidate: message.candidate,
        fromClientId: clientId,
      });
    }
  }

  /**
   * Handle streaming ready event
   */
  handleStreamingReady(clientId, message) {
    if (this.currentStream && this.currentStream.tabId === message.tabId) {
      console.log(`✅ Stream ready for tab: ${message.tabId}`);

      // Notify web client that stream is ready
      this.sendToClient(this.currentStream.webClientId, {
        type: "stream-ready",
        tabId: message.tabId,
      });
    }
  }

  /**
   * Handle request for interceptor status from web client
   */
  handleRequestInterceptorStatus(clientId, message) {
    console.log(
      `🎛️ Interceptor status request from ${clientId} for tab: ${message.tabId}`
    );

    // Forward request to control tab
    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "request-interceptor-status",
        tabId: message.tabId,
        webClientId: clientId,
      });
    } else {
      // No control tab available
      this.sendToClient(clientId, {
        type: "interceptor-status",
        enabled: false,
        enableCropping: false,
        cropRegion: null,
        error: "No control tab available",
      });
    }
  }

  /**
   * Handle toggle cropping request from web client
   */
  handleToggleInterceptorCropping(clientId, message) {
    console.log(
      `🎛️ Toggle cropping request from ${clientId} for tab: ${message.tabId}, enabled: ${message.enabled}`
    );

    // Forward request to control tab
    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "toggle-interceptor-cropping",
        tabId: message.tabId,
        enabled: message.enabled,
        webClientId: clientId,
      });
    } else {
      this.sendToClient(clientId, {
        type: "error",
        message: "No control tab available to handle interceptor commands",
      });
    }
  }

  /**
   * Handle set crop region request from web client
   */
  handleSetInterceptorCropRegion(clientId, message) {
    console.log(
      `🎛️ Set crop region request from ${clientId} for tab: ${message.tabId}`,
      message.cropRegion
    );

    // Forward request to control tab
    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "set-interceptor-crop-region",
        tabId: message.tabId,
        cropRegion: message.cropRegion,
        webClientId: clientId,
      });
    } else {
      this.sendToClient(clientId, {
        type: "error",
        message: "No control tab available to handle interceptor commands",
      });
    }
  }

  /**
   * Handle interceptor status response from control tab to web client
   */
  handleInterceptorStatusToWebClient(clientId, message) {
    console.log(
      `🎛️ Interceptor status response from control tab to web client: ${message.webClientId}`
    );

    // Forward status to web client
    if (message.webClientId && this.clients.has(message.webClientId)) {
      this.sendToClient(message.webClientId, {
        type: "interceptor-status",
        enabled: message.enabled,
        enableCropping: message.enableCropping,
        cropRegion: message.cropRegion,
      });
    } else {
      console.warn(
        `Web client ${message.webClientId} not found for interceptor status`
      );
    }
  }

  /**
   * Handle client disconnection
   */
  async handleDisconnection(clientId) {
    const client = this.clients.get(clientId);
    if (!client) return;

    console.log(`📱 Client disconnected: ${clientId} (${client.type})`);

    // Clean up based on client type
    if (client.type === "target-tab") {
      const tabId = client.metadata.tabId;
      this.targetTabs.delete(tabId);

      // Clean up current stream if it's from this tab
      if (this.currentStream && this.currentStream.tabId === tabId) {
        this.currentStream = null;
      }

      // Notify other clients
      this.broadcastToType("control-tab", {
        type: "target-tab-disconnected",
        tabId,
        clientId,
      });

      this.broadcastToType("web-client", {
        type: "target-tab-unavailable",
        tabId,
      });
    } else if (client.type === "control-tab") {
      this.controlTab = null;
    } else if (client.type === "web-client") {
      this.webClients.delete(clientId);

      // Notify control tab about web client disconnection
      if (this.controlTab) {
        this.sendToClient(this.controlTab, {
          type: "web-client-disconnected",
          webClientId: clientId,
        });
      }

      // Stop current stream if this web client was using it
      if (this.currentStream && this.currentStream.webClientId === clientId) {
        await this.stopCurrentStream();
      }
    }

    this.clients.delete(clientId);
  }

  /**
   * Send message to a specific client
   */
  sendToClient(clientId, message) {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      client.ws.send(JSON.stringify(message));
    }
  }

  /**
   * Broadcast message to all clients of a specific type
   */
  broadcastToType(type, message) {
    for (const client of this.clients.values()) {
      if (client.type === type && client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(JSON.stringify(message));
      }
    }
  }

  /**
   * Check if a tab is currently streaming
   */
  isTabStreaming(tabId) {
    return this.currentStream && this.currentStream.tabId === tabId;
  }

  /**
   * Get server statistics
   */
  getStats() {
    return {
      totalClients: this.clients.size,
      targetTabs: this.targetTabs.size,
      webClients: this.webClients.size,
      activeStreams: this.currentStream ? 1 : 0,
      hasControlTab: !!this.controlTab,
      availableTabs: this.availableTabs.size,
    };
  }

  /**
   * Stop the signaling server
   */
  async stop() {
    if (!this.isRunning) return;

    console.log("🛑 Stopping signaling server...");

    // Close all client connections
    for (const client of this.clients.values()) {
      if (client.ws.readyState === WebSocket.OPEN) {
        client.ws.close();
      }
    }

    // Close server
    if (this.wss) {
      this.wss.close();
    }

    this.isRunning = false;
    console.log("✅ Signaling server stopped");
  }

  /**
   * Handle WebRTC offer from target tab (to be forwarded to control tab)
   */
  handleWebRTCOfferFromTarget(clientId, message) {
    console.log(`🎯 WebRTC offer from target tab: ${message.targetTabId}`);

    // Forward the offer to the control tab for dual-hop architecture
    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "webrtc-offer-from-target",
        offer: message.offer,
        targetTabId: message.targetTabId,
        webClientId: this.currentStream ? this.currentStream.webClientId : null,
        fromClientId: clientId,
      });
    }
  }

  /**
   * Handle WebRTC ICE candidate from target tab (to be forwarded to control tab)
   */
  handleWebRTCIceCandidateFromTarget(clientId, message) {
    console.log(`🧊 ICE candidate from target tab: ${message.targetTabId}`);

    // Forward the ICE candidate to the control tab for dual-hop architecture
    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "webrtc-ice-candidate-from-target",
        candidate: message.candidate,
        targetTabId: message.targetTabId,
        webClientId: this.currentStream ? this.currentStream.webClientId : null,
        fromClientId: clientId,
      });
    }
  }

  /**
   * Handle WebRTC answer to target tab (from control tab)
   */
  handleWebRTCAnswerToTarget(clientId, message) {
    console.log(`📤 WebRTC answer to target tab: ${message.targetTabId}`);

    // For single-stream mode, find target tab from current stream
    if (
      this.currentStream &&
      this.currentStream.tabId === message.targetTabId
    ) {
      const targetClientId = this.targetTabs.get(this.currentStream.tabId);
      if (targetClientId) {
        this.sendToClient(targetClientId, {
          type: "webrtc-answer-to-target",
          answer: message.answer,
          targetTabId: message.targetTabId,
          fromClientId: clientId,
        });
      }
    }
  }

  /**
   * Handle WebRTC ICE candidate to target tab (from control tab)
   */
  handleWebRTCIceCandidateToTarget(clientId, message) {
    console.log(`🧊 ICE candidate to target tab: ${message.targetTabId}`);

    // For single-stream mode, find target tab from current stream
    if (
      this.currentStream &&
      this.currentStream.tabId === message.targetTabId
    ) {
      const targetClientId = this.targetTabs.get(this.currentStream.tabId);
      if (targetClientId) {
        this.sendToClient(targetClientId, {
          type: "webrtc-ice-candidate-to-target",
          candidate: message.candidate,
          targetTabId: message.targetTabId,
          fromClientId: clientId,
        });
      }
    }
  }

  /**
   * Handle WebRTC offer from control tab to web client
   */
  handleWebRTCOfferToWebClient(clientId, message) {
    console.log(
      `🎯 WebRTC offer from control tab to web client: ${message.targetClientId}`
    );

    // Send to specific web client
    if (message.targetClientId && this.clients.has(message.targetClientId)) {
      this.sendToClient(message.targetClientId, {
        type: "webrtc-offer",
        offer: message.offer,
        tabId: message.tabId,
        webClientId: message.targetClientId,
        fromClientId: clientId,
      });
    } else {
      console.warn(
        `[SignalingServer] Web client not found: ${message.targetClientId}`
      );
    }
  }

  /**
   * Handle WebRTC ICE candidate from control tab to web client
   */
  handleWebRTCIceCandidateToWebClient(clientId, message) {
    console.log(
      `🧊 ICE candidate from control tab to web client: ${message.webClientId}`
    );

    // Send to specific web client
    if (message.webClientId && this.clients.has(message.webClientId)) {
      this.sendToClient(message.webClientId, {
        type: "webrtc-ice-candidate",
        candidate: message.candidate,
        webClientId: message.webClientId,
        fromClientId: clientId,
      });
    } else {
      console.warn(
        `[SignalingServer] Web client not found: ${message.webClientId}`
      );
    }
  }

  /**
   * Handle WebRTC answer from web client to control tab
   */
  handleWebRTCAnswerFromWebClient(clientId, message) {
    console.log(
      `📤 WebRTC answer from web client to control tab for tab: ${message.tabId}`
    );

    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "webrtc-answer-from-web-client",
        answer: message.answer,
        tabId: message.tabId,
        webClientId: clientId,
        fromClientId: clientId,
      });
    }
  }

  /**
   * Handle WebRTC ICE candidate from web client to control tab
   */
  handleWebRTCIceCandidateFromWebClient(clientId, message) {
    console.log(
      `🧊 ICE candidate from web client to control tab for tab: ${message.tabId}`
    );

    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "webrtc-ice-candidate-from-web-client",
        candidate: message.candidate,
        tabId: message.tabId,
        webClientId: clientId,
        fromClientId: clientId,
      });
    }
  }
}
